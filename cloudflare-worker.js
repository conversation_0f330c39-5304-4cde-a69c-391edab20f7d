/**
 * <PERSON>flare Worker for Security Headers
 * Deploy this to Cloudflare Workers to add security headers
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // Get the original response
  const response = await fetch(request)
  
  // Create a new response with security headers
  const newResponse = new Response(response.body, response)
  
  // Add security headers
  const securityHeaders = {
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://embed.tawk.to; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https: wss: ws: https://embed.tawk.to; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self';",
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
  }
  
  // Apply headers only to HTML responses
  const contentType = newResponse.headers.get('content-type')
  if (contentType && contentType.includes('text/html')) {
    Object.entries(securityHeaders).forEach(([key, value]) => {
      newResponse.headers.set(key, value)
    })
  }
  
  return newResponse
}
