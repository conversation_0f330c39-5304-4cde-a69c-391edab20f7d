<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- ✅ عنوان احترافي -->
    <title>Pegasus Tools – Complete Software & Hardware Solution</title>

    <!-- ✅ وصف دقيق وموثوق -->
    <meta name="description" content="Pegasus Tools is a professional smartphone repair and unlocking system with integrated software tools and detailed hardware documentation for technicians worldwide." />
    <meta name="author" content="Pegasus Team" />

    <!-- ✅ OG Meta Tags (Facebook) -->
    <meta property="og:title" content="Pegasus Tools – Complete Software & Hardware Solution" />
    <meta property="og:description" content="Smartphone unlocking system with software and hardware support – built for repair professionals." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://www.pegasus-tools.com/favicon.ico" />
    <meta property="og:url" content="https://www.pegasus-tools.com" />

    <!-- ✅ Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@pegasus_tools" />
    <meta name="twitter:image" content="https://www.pegasus-tools.com/favicon.ico" />

    <!-- 🛡️ Production CSP Protection - Load FIRST -->
    <script>
      // Inline CSP protection for immediate execution
      (function() {
        if (window.location.hostname.includes('pegasus-tools.com')) {
          console.log('🛡️ Production CSP protection loading...');

          function removeCSP() {
            const cspTags = document.querySelectorAll('meta[http-equiv*="Content-Security-Policy"], meta[content*="frame-ancestors"]');
            if (cspTags.length > 0) {
              console.warn('🚨 Removing', cspTags.length, 'CSP meta tag(s)');
              cspTags.forEach(tag => tag.remove());
            }
          }

          // Immediate cleanup
          removeCSP();

          // Monitor for new tags
          const observer = new MutationObserver(removeCSP);
          observer.observe(document.documentElement, { childList: true, subtree: true });

          // Periodic cleanup
          setInterval(removeCSP, 2000);

          console.log('✅ Inline CSP protection active');
        }
      })();
    </script>

    <!-- ✅ Structured Data: Organization + Website -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Organization",
          "name": "Pegasus Tools",
          "url": "https://www.pegasus-tools.com",
          "logo": {
            "@type": "ImageObject",
            "url": "https://www.pegasus-tools.com/favicon.ico"
          },
          "sameAs": [
            "https://www.pegasus-tools.com",
            "https://twitter.com/pegasus_tools"
          ]
        },
        {
          "@type": "WebSite",
          "name": "Pegasus Tools",
          "url": "https://www.pegasus-tools.com",
          "publisher": {
            "@id": "https://www.pegasus-tools.com"
          }
        }
      ]
    }
    </script>
  </head>

  <body>
    <div id="root"></div>

    <!-- Tawk.to Live Chat with CSP protection -->
    <script type="text/javascript">
      var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();

      // Enhanced CSP protection before loading Tawk.to
      function protectFromCSPMetaTags() {
        const removeCSP = () => {
          const cspTags = document.querySelectorAll('meta[http-equiv*="Content-Security-Policy"], meta[content*="frame-ancestors"]');
          cspTags.forEach(tag => {
            console.warn('🚨 Tawk.to CSP meta tag blocked:', tag.outerHTML);
            tag.remove();
          });
        };

        // Remove any existing CSP meta tags before Tawk.to loads
        removeCSP();

        // Monitor for CSP meta tags added by Tawk.to
        const observer = new MutationObserver(() => removeCSP());
        observer.observe(document.head, { childList: true, subtree: true });

        return observer;
      }

      const tawkCSPProtection = protectFromCSPMetaTags();

      (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/68254dbfe6bf69190cdb34c4/1ir8rfd4h';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');

        // Add extra protection after script loads
        s1.onload = function() {
          setTimeout(() => {
            const cspTags = document.querySelectorAll('meta[http-equiv*="Content-Security-Policy"]');
            if (cspTags.length > 0) {
              console.warn('🚨 Removing CSP meta tags added by Tawk.to:', cspTags);
              cspTags.forEach(tag => tag.remove());
            }
          }, 100);
        };

        s0.parentNode.insertBefore(s1,s0);
      })();
    </script>

    <!-- Scripts -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
