
import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface Background3DProps {
  variant?: 'particles' | 'waves' | 'gradients';
  density?: 'low' | 'medium' | 'high';
  color?: string;
  interactive?: boolean;
  children?: React.ReactNode; // Add children prop
}

const Background3D: React.FC<Background3DProps> = ({
  variant = 'particles',
  density = 'medium',
  color = 'pegasus-orange',
  interactive = true,
  children, // Include children in destructuring
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Define densities for different variants
  const densityValues = {
    particles: { low: 15, medium: 30, high: 50 },
    waves: { low: 3, medium: 5, high: 8 },
    gradients: { low: 2, medium: 3, high: 4 },
  };
  
  const count = densityValues[variant][density];
  
  // Generate elements based on variant
  const generateElements = () => {
    switch(variant) {
      case 'particles':
        return Array.from({ length: count }).map((_, i) => (
          <Particle key={i} index={i} color={color} interactive={interactive} total={count} />
        ));
      case 'waves':
        return Array.from({ length: count }).map((_, i) => (
          <Wave key={i} index={i} color={color} interactive={interactive} total={count} />
        ));
      case 'gradients':
        return Array.from({ length: count }).map((_, i) => (
          <Gradient key={i} index={i} color={color} interactive={interactive} total={count} />
        ));
      default:
        return null;
    }
  };
  
  useEffect(() => {
    if (!interactive || !containerRef.current) return;
    
    const container = containerRef.current;
    let mouseX = 0;
    let mouseY = 0;
    let lastX = 0;
    let lastY = 0;
    
    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX - window.innerWidth / 2;
      mouseY = e.clientY - window.innerHeight / 2;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    const updateElements = () => {
      // Calculate lerped mouse position for smooth movement
      lastX += (mouseX - lastX) * 0.05;
      lastY += (mouseY - lastY) * 0.05;
      
      // Apply parallax effect to all child elements
      const elements = container.querySelectorAll('.parallax-element');
      elements.forEach((el, i) => {
        const depth = parseFloat((el as HTMLElement).dataset.depth || '0.1');
        const moveX = lastX * depth;
        const moveY = lastY * depth;
        (el as HTMLElement).style.transform = `translate3d(${moveX}px, ${moveY}px, 0)`;
      });
      
      requestAnimationFrame(updateElements);
    };
    
    const animationId = requestAnimationFrame(updateElements);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      cancelAnimationFrame(animationId);
    };
  }, [interactive]);
  
  return (
    <div 
      ref={containerRef}
      className="fixed inset-0 pointer-events-none overflow-hidden -z-10"
    >
      {generateElements()}
      {/* Add children here */}
      {children}
    </div>
  );
};

// Particle component
const Particle: React.FC<{
  index: number;
  color: string;
  interactive: boolean;
  total: number;
}> = ({ index, color, interactive, total }) => {
  // Calculate depth based on index
  const depth = 0.05 + (0.1 * (index / total));
  
  // Calculate size based on depth
  const size = 5 + (20 * depth);
  
  // Calculate initial position
  const x = Math.random() * 100;
  const y = Math.random() * 100;
  
  return (
    <motion.div
      className={`absolute rounded-full parallax-element bg-${color}`}
      style={{
        width: size,
        height: size,
        left: `${x}%`,
        top: `${y}%`,
        opacity: 0.2 + (depth * 0.5),
      }}
      data-depth={depth.toString()}
      animate={!interactive ? {
        y: [0, -20, 0],
        x: [0, 10, 0],
      } : undefined}
      transition={!interactive ? {
        duration: 5 + Math.random() * 5,
        repeat: Infinity,
        ease: "easeInOut",
        delay: index * 0.1,
      } : undefined}
    />
  );
};

// Wave component
const Wave: React.FC<{
  index: number;
  color: string;
  interactive: boolean;
  total: number;
}> = ({ index, color, interactive, total }) => {
  // Calculate depth and opacity based on index
  const depth = 0.03 + (0.07 * (index / total));
  const opacity = 0.03 + (0.05 * (index / total));
  
  // Position from bottom
  const bottomOffset = 10 + (index * 5);
  
  return (
    <motion.div
      className={`absolute w-[200%] h-[100px] left-[-50%] rounded-[100%] parallax-element bg-${color}`}
      style={{
        bottom: `-${bottomOffset}px`,
        opacity,
      }}
      data-depth={depth.toString()}
      animate={!interactive ? {
        x: ['-10%', '0%', '-10%'],
      } : undefined}
      transition={!interactive ? {
        duration: 20,
        repeat: Infinity,
        ease: "easeInOut",
        delay: index * 2,
      } : undefined}
    />
  );
};

// Gradient component
const Gradient: React.FC<{
  index: number;
  color: string;
  interactive: boolean;
  total: number;
}> = ({ index, color, interactive, total }) => {
  // Calculate depth and size based on index
  const depth = 0.02 + (0.05 * (index / total));
  const size = 300 + (index * 100);
  
  // Calculate position
  const x = 20 + (Math.random() * 60);
  const y = 20 + (Math.random() * 60);
  
  return (
    <motion.div
      className={`absolute rounded-full parallax-element bg-${color} filter blur-[100px] mix-blend-overlay`}
      style={{
        width: size,
        height: size,
        left: `${x}%`,
        top: `${y}%`,
        opacity: 0.15,
      }}
      data-depth={depth.toString()}
      animate={!interactive ? {
        x: [0, 30, 0],
        y: [0, -20, 0],
      } : undefined}
      transition={!interactive ? {
        duration: 15 + Math.random() * 10,
        repeat: Infinity,
        ease: "easeInOut",
        delay: index * 2,
      } : undefined}
    />
  );
};

export default Background3D;

