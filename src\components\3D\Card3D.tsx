
import React from 'react';
import { use3DEffect } from '@/hooks/use3DEffect';
import { cn } from '@/lib/utils';

interface Card3DProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  intensity?: number;
  perspective?: number;
  glare?: boolean;
  depth?: 'none' | 'low' | 'medium' | 'high';
  hover?: 'none' | 'lift' | 'glow' | 'scale';
  variant?: 'default' | 'glass' | 'solid' | 'gradient';
}

const Card3D: React.FC<Card3DProps> = ({
  children,
  className,
  intensity = 15,
  perspective = 1000,
  glare = true,
  depth = 'medium',
  hover = 'lift',
  variant = 'default',
  ...props
}) => {
  const ref = use3DEffect({
    intensity,
    perspective,
    glare,
    scale: hover === 'scale',
  });

  const depthClasses = {
    none: 'shadow-none',
    low: 'shadow-md after:shadow-sm',
    medium: 'shadow-lg after:shadow-md',
    high: 'shadow-xl after:shadow-lg',
  };

  const hoverClasses = {
    none: '',
    lift: 'transition-transform hover:-translate-y-2',
    glow: 'transition-shadow hover:shadow-[0_0_15px_rgba(249,115,22,0.6)]',
    scale: 'transition-transform',
  };

  const variantClasses = {
    default: 'bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700',
    glass: 'bg-gray-50/70 dark:bg-gray-800/70 backdrop-blur-md border border-gray-200/20 dark:border-gray-700/30',
    solid: 'bg-pegasus-orange text-white',
    gradient: 'bg-gradient-to-br from-pegasus-orange to-pegasus-orange-600 text-white',
  };

  return (
    <div
      ref={ref}
      className={cn(
        'rounded-xl p-6 relative transform transition-all will-change-transform',
        depthClasses[depth],
        hoverClasses[hover],
        variantClasses[variant],
        // 3D depth effect with pseudo-element
        'after:content-[""] after:absolute after:inset-0 after:rounded-xl after:-z-10 after:translate-z-[-20px] after:bg-white/5 dark:after:bg-black/5 after:blur-sm',
        className
      )}
      style={{
        transformStyle: 'preserve-3d',
      }}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card3D;
