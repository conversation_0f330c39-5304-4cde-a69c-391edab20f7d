
import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useInView } from '@/hooks/useInView';

interface Section3DProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  depth?: 'none' | 'low' | 'medium' | 'high';
  stagger?: boolean;
  perspective?: boolean;
  delay?: number;
}

const Section3D: React.FC<Section3DProps> = ({
  children,
  className,
  id,
  depth = 'medium',
  stagger = true,
  perspective = true,
  delay = 0,
}) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: false,
    rootMargin: '0px 0px -10% 0px'
  });

  const depthClasses = {
    none: '',
    low: 'shadow-md',
    medium: 'shadow-lg',
    high: 'shadow-xl',
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1],
        delay,
        staggerChildren: stagger ? 0.15 : 0,
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, z: -50 },
    visible: { 
      opacity: 1, 
      y: 0, 
      z: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section id={id} className={cn('relative py-16 overflow-hidden', className)}>
      <motion.div
        ref={ref}
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className={cn(
          'w-full rounded-xl p-6',
          depthClasses[depth],
          perspective && 'transform-gpu'
        )}
        style={perspective ? { 
          perspective: '1000px',
          perspectiveOrigin: 'center'
        } : {}}
      >
        {React.Children.map(children, (child, index) => {
          if (!React.isValidElement(child)) return child;
          return (
            <motion.div
              key={index}
              variants={itemVariants}
              style={perspective ? { transformStyle: 'preserve-3d' } : {}}
              className="will-change-transform"
            >
              {child}
            </motion.div>
          );
        })}
      </motion.div>
    </section>
  );
};

export default Section3D;
