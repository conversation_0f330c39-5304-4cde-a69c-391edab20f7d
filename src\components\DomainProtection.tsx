import React, { useEffect, useState } from 'react';
import { Shield, AlertTriangle, Globe } from 'lucide-react';
import { cloudflareProtection } from '@/utils/cloudflareProtection';

interface DomainProtectionProps {
  children: React.ReactNode;
}

export const DomainProtection: React.FC<DomainProtectionProps> = ({ children }) => {
  const [isDomainValid, setIsDomainValid] = useState<boolean | null>(null);
  const [currentDomain, setCurrentDomain] = useState<string>('');

  useEffect(() => {
    const validateDomain = () => {
      const domainInfo = cloudflareProtection.getDomainInfo();
      setCurrentDomain(domainInfo.domain);
      setIsDomainValid(domainInfo.isAuthorized);
    };

    validateDomain();

    // Listen for domain changes (in case of redirects)
    const handleLocationChange = () => {
      validateDomain();
    };

    window.addEventListener('popstate', handleLocationChange);
    return () => window.removeEventListener('popstate', handleLocationChange);
  }, []);

  // Show loading while checking
  if (isDomainValid === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-lg">Verifying domain security...</p>
        </div>
      </div>
    );
  }

  // Show error if domain is not allowed
  if (!isDomainValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-red-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 text-center">
            {/* Warning Icon */}
            <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-10 h-10 text-red-400" />
            </div>

            {/* Title */}
            <h1 className="text-3xl font-bold text-white mb-4">
              Access Denied
            </h1>

            {/* Description */}
            <p className="text-red-200 mb-6 leading-relaxed">
              This application is restricted to authorized domains only. 
              Access from unauthorized domains is blocked for security reasons.
            </p>

            {/* Domain Info */}
            <div className="bg-white/5 rounded-lg p-4 mb-6 border border-white/10">
              <div className="flex items-center justify-center mb-2">
                <Globe className="w-4 h-4 text-green-400 mr-2" />
                <span className="text-sm font-medium text-green-400">Authorized Domain</span>
              </div>
              <p className="text-white font-mono text-sm">
                www.pegasus-tools.com
              </p>
            </div>

            {/* Current Domain */}
            <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="w-4 h-4 text-red-400 mr-2" />
                <span className="text-sm font-medium text-red-400">Current Domain</span>
              </div>
              <p className="text-red-200 font-mono text-sm">
                {currentDomain}
              </p>
            </div>

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-white/10">
              <div className="flex items-center justify-center text-white/60 text-xs">
                <Shield className="w-3 h-3 mr-1" />
                <span>Protected by Cloudflare Security</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show success indicator for valid domains (development only)
  const isDevelopment = !import.meta.env.PROD;
  const isLocalhost = currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1');
  const showSuccessIndicator = isDevelopment && isLocalhost;

  return (
    <>
      {showSuccessIndicator && (
        <div className="fixed top-4 right-4 z-50">
          <div className="bg-green-500/10 backdrop-blur-sm border border-green-500/20 rounded-lg px-4 py-2 flex items-center space-x-2">
            <Shield className="w-4 h-4 text-green-400" />
            <div className="flex flex-col">
              <span className="text-green-400 text-sm font-medium">
                Development Mode
              </span>
              <span className="text-green-300 text-xs">
                Domain: {currentDomain}
              </span>
            </div>
          </div>
        </div>
      )}
      {children}
    </>
  );
};

// Hook for domain validation
export const useDomainValidation = () => {
  const [isDomainValid, setIsDomainValid] = useState<boolean>(false);
  const [currentDomain, setCurrentDomain] = useState<string>('');

  useEffect(() => {
    const hostname = window.location.hostname;
    const allowedDomains = [
      'www.pegasus-tools.com',
      'pegasus-tools.com',
      'localhost',
      '127.0.0.1'
    ];

    setCurrentDomain(hostname);
    setIsDomainValid(allowedDomains.includes(hostname));
  }, []);

  return {
    isDomainValid,
    currentDomain,
    allowedDomain: 'www.pegasus-tools.com'
  };
};

export default DomainProtection;
