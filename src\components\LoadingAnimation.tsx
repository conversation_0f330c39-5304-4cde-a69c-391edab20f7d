
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const LoadingAnimation: React.FC = () => {
  const [animationComplete, setAnimationComplete] = useState(false);

  // Colors
  const outlineColor = "#FFFFFF";
  const purpleColor = "#C084FC";

  // Simplified and faster animation variants
  const pathVariants = {
    hidden: {
      pathLength: 0,
      opacity: 0,
      fill: "rgba(192, 132, 252, 0)"
    },
    visible: (i: number) => ({
      pathLength: 1,
      opacity: 1,
      fill: "rgba(192, 132, 252, 1)",
      transition: {
        pathLength: {
          duration: 1,
          delay: i * 0.1
        },
        opacity: {
          duration: 0.3,
          delay: i * 0.1
        },
        fill: {
          duration: 0.5,
          delay: 1 + i * 0.1
        }
      }
    })
  };

  // Simplified particles - reduced count for better performance
  const particles = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 3 + 1,
    duration: Math.random() * 2 + 1,
    delay: Math.random() * 1,
    opacity: Math.random() * 0.5 + 0.2
  }));

  // Path data for "Pegasus Tool" logo
  const pegasusToolPath = "m360.6 212.1h-25.7v20.9h-20.1v-20.8l19.2-17.7h23.5q4.1 0 6.5-2.4 2.6-2.5 2.6-6.6 0-4.1-2.6-6.3-2.4-2.2-6.5-2.2h-28l-14.7-17.6h45.8q5.9 0 10.7 1.9 4.8 1.9 8.2 5.4 3.5 3.5 5.3 8.3 1.9 4.8 1.9 10.7 0 6-1.9 10.9-1.8 4.9-5.3 8.3-3.4 3.4-8.2 5.3-4.8 1.9-10.7 1.9zm35.6-52.7h62.3v17.6h-42.1v9.3h42.1l-18.8 17.6h-23.2v11.6h42v17.5h-62.3zm110.9 75.7q-8.8 0-16.3-2.8-7.5-2.8-13.1-8-5.5-5.1-8.7-12.3-3.1-7.1-3.1-15.9 0-8.7 3.1-15.9 3.2-7.1 8.7-12.2 5.6-5.1 13.2-7.9 7.5-2.8 16.3-2.8 8.5 0 16.1 2.6 7.8 2.7 13 7.6l-14.1 12.8q-2.5-2.6-6-4-3.5-1.5-7.9-1.5-4.5 0-8.5 1.6-4 1.5-7 4.3-3.1 2.9-4.9 6.9-1.7 3.9-1.7 8.6 0 4.8 1.7 8.8 1.8 3.9 4.9 6.7 3 2.8 7 4.3 4.1 1.5 8.6 1.5 1.2 0 2.8-0.1 1.5-0.1 3.1-0.3 1.6-0.2 3-0.4 1.4-0.3 2.4-0.8v-8.6l-15.4-17.6h35.7v43.3h-14.4q-1.6 0.5-4 0.9-2.5 0.3-5.1 0.6-2.5 0.2-5 0.5-2.5 0.1-4.4 0.1zm42.3-2.1l10.7-38.5h31.1l-9.8-35.1h20.9l20.7 73.6h-21l-5.9-21h-19.9l-5.9 21zm116-45.9q8.2 0 13.9 2 5.7 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.7 4.1 1.7 8.6 0 4.5-1.8 8.9-1.7 4.4-5.6 8-3.9 3.5-10.2 5.8-6.3 2.2-15.3 2.2-4.4 0-8.5-0.9-4.1-0.9-7.6-2.4-3.5-1.5-6.3-3.3-2.7-1.7-4.7-3.6l14.1-12.9q2.1 2.5 5.4 4 3.4 1.5 8.3 1.5 5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.5-2-11.2-2-8.3 0-14-1.9-5.7-2-9.2-5.1-3.5-3.3-5.1-7.4-1.6-4.2-1.6-8.5 0-4.5 1.7-8.9 1.8-4.5 5.7-8.1 3.9-3.6 10.2-5.8 6.3-2.2 15.3-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.4 3.5 1.4 6.2 3.2 2.8 1.8 4.8 3.7l-14 12.8q-2.1-2.4-5.5-3.9-3.4-1.6-8.3-1.6-5.7 0-8.8 1.9-3.1 1.8-3.1 4 0 2.4 3.6 4.4 3.6 2 11.2 2zm91.5-8.9l20.1-18.8v40.2q0 8.9-3.1 15.5-3.1 6.6-8.2 11.1-5.1 4.4-11.6 6.7-6.4 2.2-13.2 2.2-6.7 0-13.3-2.2-6.5-2.3-11.6-6.7-5.1-4.5-8.2-11.1-3.1-6.6-3.1-15.5v-40.2h20.2v38.9q0 4.8 1.4 8.4 1.3 3.6 3.6 6 2.3 2.4 5.1 3.7 2.9 1.1 5.9 1.1 3 0 5.9-1.1 2.8-1.3 5-3.7 2.3-2.4 3.7-6 1.4-3.6 1.4-8.4zm64.7 8.9q8.2 0 13.9 2 5.6 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.6 4.1 1.6 8.5 0 4.5-1.8 9-1.6 4.4-5.5 7.9-3.9 3.6-10.2 5.8-6.3 2.2-15.4 2.2-4.4 0-8.5-0.8-4.1-1-7.5-2.4-3.5-1.5-6.3-3.3-2.8-1.8-4.8-3.7l14.1-12.8q2.1 2.4 5.5 4 3.3 1.5 8.3 1.5 5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.6-2-11.3-2-8.3 0-13.9-1.9-5.7-2-9.3-5.2-3.5-3.2-5-7.3-1.6-4.2-1.6-8.5 0-4.5 1.7-9 1.8-4.5 5.6-8 3.9-3.6 10.2-5.8 6.3-2.2 15.4-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.3 3.4 1.5 6.2 3.3 2.8 1.8 4.8 3.7l-14.1 12.8q-2.1-2.4-5.4-3.9-3.4-1.6-8.4-1.6-5.6 0-8.8 1.9-3 1.8-3 4 0 2.4 3.5 4.4 3.6 2 11.3 2zm-508.9 80.8v-17.5h72.3v17.5h-25.6v37.4l-20.1 18.6v-56zm76.2 19.1q0-8.7 2.8-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.8 0 16 2.8 7.1 2.7 12.3 7.9 5.1 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.8 7.2-7.9 12.3-5.2 5.2-12.3 8-7.2 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.8-7.1-2.8-16zm20.3 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.6 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.5-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm64.9-0.2q0-8.7 2.7-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.9 0 16 2.8 7.2 2.7 12.3 7.9 5.2 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.7 7.2-7.9 12.3-5.1 5.2-12.3 8-7.1 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.7-7.1-2.7-16zm20.2 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.7 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.4-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm65-18l20.3-18.8v56h42v17.5h-62.3z";

  // Simplified animation timing - faster completion
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationComplete(true);
    }, 2000); // Much faster - 2 seconds instead of 4.5+

    return () => clearTimeout(timer);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 1 }}
      animate={animationComplete ? { opacity: 0 } : { opacity: 1 }}
      transition={{ duration: 0.3 }}
      onAnimationComplete={() => {
        if (animationComplete) {
          document.body.classList.remove('loading');
          document.body.classList.add('loaded');

          const loadingRoot = document.getElementById('loading-root');
          if (loadingRoot && loadingRoot.parentNode) {
            loadingRoot.parentNode.removeChild(loadingRoot);
          }
        }
      }}
      className="fixed inset-0 flex flex-col items-center justify-center bg-[#111111] z-50"
    >
      {/* Simplified background particles */}
      <div className="absolute inset-0 pointer-events-none">
        {particles.map(particle => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full bg-purple-400"
            initial={{
              x: `${particle.x}%`,
              y: `${particle.y}%`,
              opacity: 0,
              scale: 0
            }}
            animate={{
              opacity: [0, particle.opacity, 0],
              scale: [0, 1, 0]
            }}
            transition={{
              duration: particle.duration,
              delay: particle.delay,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            style={{
              width: particle.size,
              height: particle.size
            }}
          />
        ))}
      </div>

      {/* Simple glow behind the logo */}
      <motion.div
        className="absolute w-60 h-60 bg-purple-400 rounded-full filter blur-[100px] opacity-20"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.2, 0.1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Simplified logo container */}
      <div className="w-full max-w-md relative">
        <motion.svg
          viewBox="0 0 930 465"
          className="w-full drop-shadow-xl"
          initial="hidden"
          animate="visible"
        >
          {/* Logo paths with simplified animation */}
          <motion.path
            d={pegasusToolPath}
            stroke={outlineColor}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="transparent"
            custom={0}
            variants={pathVariants}
          />

          <motion.path
            d="m166.7 114q3.7 0 6.7 2.1 47.6 27.2 94.6 55.4 0.8 39.6 0 79.2-30.3 18.6-61.3 36.3-3.8 2.7-7.1-0.4-0.4-37.5-0.8-75.1-16.5-10.3-33.4-20-0.4 79.2-0.8 158.4-1.4 2.1-3.7 2.5-30.6-17.4-60.9-35.4-3.1-1.5-4.6-4.6-0.8-77.5 0-155 1.7-4.6 6.3-3 29.1 17.1 58.3 34.2 2.4 1.6 5 1.7-1.4-36.4-0.4-73 0.8-1.8 2.1-3.3z"
            stroke={outlineColor}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="transparent"
            custom={1}
            variants={pathVariants}
          />
        </motion.svg>
      </div>

      {/* Simple loading text */}
      <div className="absolute bottom-10 left-0 right-0 text-center">
        <motion.div
          className="w-48 h-1 mx-auto bg-gray-800 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.3 }}
        >
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: 2, ease: "easeOut" }}
            className="h-full bg-purple-400 rounded-full"
          />
        </motion.div>
        <motion.p
          className="text-white mt-3 font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.3 }}
        >
          Loading...
        </motion.p>
      </div>
    </motion.div>
  );
};

export default LoadingAnimation;
