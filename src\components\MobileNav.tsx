import React from 'react';
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerClose, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { X, Menu, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface MenuItem {
  title: string;
  href: string;
}

interface ExpandableMenuItem {
  title: string;
  href?: string;
  subItems?: MenuItem[];
}

interface MobileNavProps {
  menuItems: MenuItem[];
  expandableMenuItems?: ExpandableMenuItem[];
}

const MobileNav: React.FC<MobileNavProps> = ({ menuItems, expandableMenuItems }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [open, setOpen] = React.useState(false);
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);

  // Function to get colors based on current page
  const getColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      return {
        text: 'text-white-400',
        hover: 'hover:bg-purple-500/10',
        active: 'bg-purple-500/10 text-purple-400',
        border: 'border-purple-500/20'
      };
    } else if (currentPath === '/software') {
      return {
        text: 'text-white-400',
        hover: 'hover:bg-orange-500/10',
        active: 'bg-orange-500/10 text-orange-400',
        border: 'border-orange-500/20'
      };
    } else if (currentPath === '/hardware') {
      return {
        text: 'text-white-400',
        hover: 'hover:bg-blue-500/10',
        active: 'bg-blue-500/10 text-blue-400',
        border: 'border-blue-500/20'
      };
    } else {
      return {
        text: 'text-white-400',
        hover: 'hover:bg-purple-500/10',
        active: 'bg-purple-500/10 text-purple-400',
        border: 'border-purple-500/20'
      };
    }
  };

  const colors = getColors();

  const handleNavigation = (href: string) => {
    setOpen(false);
    setTimeout(() => {
      if (href.startsWith('#')) {
        const element = document.getElementById(href.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
          return;
        }
        if (location.pathname !== '/') {
          navigate('/', { state: { scrollTo: href.substring(1) } });
        }
      } else {
        navigate(href);
      }
    }, 300);
  };

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  return (
    <div className="md:hidden">
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "ml-2 transition-all duration-300",
              colors.text,
              "hover:bg-black/10 backdrop-blur-sm",
              open && "bg-black/10"
            )}
            aria-label="Toggle menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="h-[80vh] p-0 bg-black/80 backdrop-blur-md">
          <div className="container mx-auto p-4">
            <div className="flex justify-end mb-4">
              <DrawerClose asChild>
                <Button 
                  variant="ghost" 
                  size="icon"
                  className={cn(
                    "transition-all duration-300",
                    colors.text,
                    "hover:bg-black/10"
                  )}
                >
                  <X className="h-5 w-5" />
                </Button>
              </DrawerClose>
            </div>
            <div className="flex flex-col space-y-2">
              <AnimatePresence mode="wait">
                {open && (
                  <>
                    {/* Home item first */}
                    {menuItems.filter(item => item.title === "Home").map((item, index) => (
                      <motion.button
                        key={item.title}
                        onClick={() => handleNavigation(item.href)}
                        className={cn(
                          "py-3 px-4 font-medium text-sm transition-all duration-300 rounded-lg text-left",
                          colors.text,
                          colors.hover,
                          (location.pathname === item.href ||
                           (location.pathname === '/' && item.href === '/')) &&
                          colors.active
                        )}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {item.title}
                      </motion.button>
                    ))}

                    {/* Product expandable menu item right after Home */}
                    {expandableMenuItems?.map((item, index) => (
                      <div key={item.title}>
                        <motion.button
                          onClick={() => item.subItems ? toggleExpanded(item.title) : item.href && handleNavigation(item.href)}
                          className={cn(
                            "w-full py-3 px-4 font-medium text-sm transition-all duration-300 rounded-lg text-left flex items-center justify-between",
                            colors.text,
                            colors.hover,
                            item.subItems?.some(subItem => location.pathname === subItem.href) && colors.active
                          )}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: (1 + index) * 0.1 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {item.title}
                          {item.subItems && (
                            <ChevronDown
                              className={cn(
                                "h-4 w-4 transition-transform duration-200",
                                expandedItems.includes(item.title) && "rotate-180"
                              )}
                            />
                          )}
                        </motion.button>

                        {/* Sub items */}
                        <AnimatePresence>
                          {item.subItems && expandedItems.includes(item.title) && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                              className="ml-4 mt-1 space-y-1"
                            >
                              {item.subItems.map((subItem, subIndex) => (
                                <motion.button
                                  key={subItem.title}
                                  onClick={() => handleNavigation(subItem.href)}
                                  className={cn(
                                    "w-full py-2 px-3 font-medium text-sm transition-all duration-300 rounded-lg text-left",
                                    "text-gray-400 hover:text-white",
                                    colors.hover,
                                    location.pathname === subItem.href && colors.active
                                  )}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: subIndex * 0.05 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  {subItem.title}
                                </motion.button>
                              ))}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    ))}

                    {/* Rest of regular menu items (excluding Home) */}
                    {menuItems.filter(item => item.title !== "Home").map((item, index) => (
                      <motion.button
                        key={item.title}
                        onClick={() => handleNavigation(item.href)}
                        className={cn(
                          "py-3 px-4 font-medium text-sm transition-all duration-300 rounded-lg text-left",
                          colors.text,
                          colors.hover,
                          (location.pathname === item.href ||
                           (location.pathname === '/' && item.href === '/')) &&
                          colors.active
                        )}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: (2 + expandableMenuItems?.length + index) * 0.1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {item.title}
                      </motion.button>
                    ))}
                  </>
                )}
              </AnimatePresence>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default MobileNav;
