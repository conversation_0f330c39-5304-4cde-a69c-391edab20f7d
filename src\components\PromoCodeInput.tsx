import React, { useState } from 'react';
import { Tag, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface PromoCodeInputProps {
  onApplyCode: (code: string) => Promise<void>;
  appliedCode: string | null;
  theme: 'hardware' | 'software';
}

export const PromoCodeInput: React.FC<PromoCodeInputProps> = ({
  onApplyCode,
  appliedCode,
  theme
}) => {
  const [promoCode, setPromoCode] = useState('');
  const [isApplying, setIsApplying] = useState(false);

  const themeColors = {
    hardware: {
      primary: 'bg-pegasus-blue-500 hover:bg-pegasus-blue-600 focus:ring-pegasus-blue-500',
      secondary: 'text-pegasus-blue-400 border-pegasus-blue-500/30',
      accent: 'bg-pegasus-blue-500/10 border-pegasus-blue-500/20'
    },
    software: {
      primary: 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500',
      secondary: 'text-orange-400 border-orange-500/30',
      accent: 'bg-orange-500/10 border-orange-500/20'
    }
  };

  const colors = themeColors[theme];

  const handleApply = async () => {
    if (!promoCode.trim()) return;
    
    setIsApplying(true);
    try {
      await onApplyCode(promoCode.trim());
      setPromoCode('');
    } catch (error) {
      console.error('Error applying promo code:', error);
    } finally {
      setIsApplying(false);
    }
  };

  const handleRemove = async () => {
    setIsApplying(true);
    try {
      await onApplyCode('');
    } catch (error) {
      console.error('Error removing promo code:', error);
    } finally {
      setIsApplying(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApply();
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {appliedCode ? (
        // Applied promo code display
        <div className={`flex items-center justify-between p-4 rounded-lg border-2 ${colors.accent} ${colors.secondary}`}>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${colors.primary.split(' ')[0]}/20`}>
              <Check className={`w-4 h-4 ${colors.secondary.split(' ')[0]}`} />
            </div>
            <div>
              <p className="text-white font-medium">Promo Code Applied</p>
              <p className={`text-sm ${colors.secondary.split(' ')[0]}`}>
                Code: {appliedCode}
              </p>
            </div>
          </div>
          <Button
            onClick={handleRemove}
            disabled={isApplying}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white hover:bg-gray-700/50"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      ) : (
        // Promo code input
        <div className="space-y-3">
          <div className="flex items-center justify-center space-x-2 text-gray-400 mb-2">
            <Tag className="w-4 h-4" />
            <span className="text-sm">Have a promo code?</span>
          </div>
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Enter promo code"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                onKeyPress={handleKeyPress}
                className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-gray-500 focus:ring-gray-500"
                disabled={isApplying}
              />
            </div>
            <Button
              onClick={handleApply}
              disabled={!promoCode.trim() || isApplying}
              className={`px-6 ${colors.primary} text-white font-medium transition-all duration-200`}
            >
              {isApplying ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Applying...</span>
                </div>
              ) : (
                'Apply'
              )}
            </Button>
          </div>
          <p className="text-xs text-gray-500 text-center">
            Enter your promo code to see available discounts
          </p>
        </div>
      )}
    </div>
  );
};

export default PromoCodeInput;
