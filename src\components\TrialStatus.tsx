import React from 'react';
import { Clock, Star, Zap, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { type TrialInfo } from '@/utils/trialUtils';

interface TrialStatusProps {
  trialInfo: TrialInfo;
  onUpgrade?: () => void;
}

export const TrialStatus: React.FC<TrialStatusProps> = ({ trialInfo, onUpgrade }) => {
  const { isTrialActive, daysRemaining, subscriptionStatus, trialEndDate } = trialInfo;

  // Calculate progress percentage (14 days total)
  const totalTrialDays = 14;
  const daysUsed = totalTrialDays - daysRemaining;
  const progressPercentage = (daysUsed / totalTrialDays) * 100;

  // Determine status color and icon
  const getStatusConfig = () => {
    if (subscriptionStatus === 'active') {
      return {
        color: 'bg-green-500',
        textColor: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: <Star className="w-5 h-5" />,
        title: 'Premium Active',
        description: 'You have full access to all features'
      };
    }
    
    if (subscriptionStatus === 'expired') {
      return {
        color: 'bg-red-500',
        textColor: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: <AlertTriangle className="w-5 h-5" />,
        title: 'Trial Expired',
        description: 'Your free trial has ended. Upgrade to continue using our services.'
      };
    }

    // Trial active
    const isUrgent = daysRemaining <= 3;
    return {
      color: isUrgent ? 'bg-orange-500' : 'bg-blue-500',
      textColor: isUrgent ? 'text-orange-600' : 'text-blue-600',
      bgColor: isUrgent ? 'bg-orange-50' : 'bg-blue-50',
      borderColor: isUrgent ? 'border-orange-200' : 'border-blue-200',
      icon: isUrgent ? <AlertTriangle className="w-5 h-5" /> : <Zap className="w-5 h-5" />,
      title: isUrgent ? 'Trial Ending Soon' : 'Free Trial Active',
      description: isUrgent 
        ? `Only ${daysRemaining} days left in your trial`
        : `${daysRemaining} days remaining in your free trial`
    };
  };

  const statusConfig = getStatusConfig();

  if (subscriptionStatus === 'active') {
    return (
      <Card className={`${statusConfig.bgColor} ${statusConfig.borderColor} border-2`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`${statusConfig.textColor}`}>
                {statusConfig.icon}
              </div>
              <CardTitle className={`text-lg ${statusConfig.textColor}`}>
                {statusConfig.title}
              </CardTitle>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Premium
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            {statusConfig.description}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${statusConfig.bgColor} ${statusConfig.borderColor} border-2`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`${statusConfig.textColor}`}>
              {statusConfig.icon}
            </div>
            <CardTitle className={`text-lg ${statusConfig.textColor}`}>
              {statusConfig.title}
            </CardTitle>
          </div>
          <Badge 
            variant={subscriptionStatus === 'expired' ? 'destructive' : 'secondary'}
            className={
              subscriptionStatus === 'expired' 
                ? 'bg-red-100 text-red-800' 
                : daysRemaining <= 3 
                  ? 'bg-orange-100 text-orange-800'
                  : 'bg-blue-100 text-blue-800'
            }
          >
            {subscriptionStatus === 'expired' ? 'Expired' : 'Trial'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">
          {statusConfig.description}
        </p>

        {isTrialActive && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>Trial Progress</span>
              </span>
              <span className="font-medium">
                {daysUsed}/{totalTrialDays} days used
              </span>
            </div>
            <Progress 
              value={progressPercentage} 
              className="h-2"
            />
            {trialEndDate && (
              <p className="text-xs text-gray-500">
                Trial ends on {trialEndDate.toLocaleDateString()}
              </p>
            )}
          </div>
        )}

        {subscriptionStatus === 'expired' && (
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h4 className="font-medium text-gray-900 mb-2">What happens now?</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Your account is now limited</li>
              <li>• No access to premium features</li>
              <li>• Upgrade to restore full functionality</li>
            </ul>
          </div>
        )}

        {(subscriptionStatus === 'trial' || subscriptionStatus === 'expired') && onUpgrade && (
          <Button 
            onClick={onUpgrade}
            className={`w-full ${
              subscriptionStatus === 'expired' 
                ? 'bg-red-600 hover:bg-red-700' 
                : daysRemaining <= 3
                  ? 'bg-orange-600 hover:bg-orange-700'
                  : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {subscriptionStatus === 'expired' ? 'Upgrade Now' : 'Upgrade to Premium'}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default TrialStatus;
