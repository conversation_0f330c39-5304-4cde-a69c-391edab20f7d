import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { User, LogIn } from "lucide-react";
import { Link } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

import { UserAvatar } from "./UserAvatar";

export const AuthButton = () => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Function to get button colors based on current page
  const getButtonColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      // Home page - Purple theme
      return {
        primary: 'bg-purple-500 hover:bg-purple-700 text-white-400',
      };
    } else if (currentPath === '/software') {
      // Software page - Orange theme
      return {
        primary: 'bg-orange-500 hover:bg-orange-700 text-white-400',
      };
    } else if (currentPath === '/hardware') {
      // Hardware page - Blue theme
      return {
        primary: 'bg-blue-500 hover:bg-blue-700 text-white-400',
      };
    } else {
      // Default - Purple theme
      return {
        primary: 'bg-purple-500 hover:bg-purple-700 text-white-400',
      };
    }
  };

  const buttonColors = getButtonColors();

  // Show loading state while auth is loading
  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="animate-pulse bg-gray-700 rounded-full h-9 w-9"></div>
      </div>
    );
  }

  return (
    <>
      {!user && (
        <div className="flex items-center gap-3">
          <Link to="/sign-in">
            <Button
              size="sm"
              className={cn(
                "px-4 py-2 rounded-md transition-all duration-300",
                "backdrop-blur-sm border border-transparent",
                "hover:border-current/20",
                buttonColors.primary
              )}
            >
              <User className="w-4 h-4 mr-2" />
              Get Started
            </Button>
          </Link>
        </div>
      )}
      {user && (
        <UserAvatar />
      )}
    </>
  );
};

export default AuthButton;
