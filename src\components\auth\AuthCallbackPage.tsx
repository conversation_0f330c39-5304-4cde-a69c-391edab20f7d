import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

export const AuthCallbackPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Parse URL parameters from both query string and hash fragment
        const parseUrlParams = () => {
          const params = {};

          // Get query parameters
          searchParams.forEach((value, key) => {
            params[key] = value;
          });

          // Get hash fragment parameters
          const hash = window.location.hash.substring(1);
          if (hash) {
            const hashParams = new URLSearchParams(hash);
            hashParams.forEach((value, key) => {
              params[key] = value;
            });
          }

          return params;
        };

        const allParams = parseUrlParams();
        const type = allParams.type;
        const token = allParams.token;
        const accessToken = allParams.access_token;
        const refreshToken = allParams.refresh_token;
        const error = allParams.error;
        const errorDescription = allParams.error_description;

        // Handle errors first
        if (error) {
          toast.error(errorDescription || error);
          navigate('/sign-in');
          return;
        }

        // Handle password recovery
        if (type === 'recovery') {

          // If we have access token and refresh token, set the session first
          if (accessToken && refreshToken) {
            try {
              const { error: sessionError } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken,
              });

              if (sessionError) {
                toast.error('Invalid or expired reset link. Please request a new password reset.');
                navigate('/forgot-password');
                return;
              }

              navigate('/reset-password');
              return;
            } catch (err) {
              toast.error('Invalid reset link. Please request a new password reset.');
              navigate('/forgot-password');
              return;
            }
          }

          // If we have a token hash, verify it
          if (token) {
            try {
              // Verify the recovery token and set the session
              const { data, error: verifyError } = await supabase.auth.verifyOtp({
                token_hash: token,
                type: 'recovery'
              });

              if (verifyError) {
                toast.error('Invalid or expired reset link. Please request a new password reset.');
                navigate('/forgot-password');
                return;
              }

              // Redirect to reset password page
              navigate('/reset-password');
              return;
            } catch (err) {
              toast.error('Invalid reset link. Please request a new password reset.');
              navigate('/forgot-password');
              return;
            }
          }

          // If no tokens, just redirect to reset password
          navigate('/reset-password');
          return;
        }

        // Handle email confirmation
        if (type === 'signup' || type === 'email_change') {
          if (token) {
            const { error: verifyError } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: type
            });

            if (verifyError) {
              toast.error('Email verification failed. Please try again.');
              navigate('/sign-in');
              return;
            }

            toast.success('Email verified successfully!');
            navigate('/');
            return;
          }
        }

        // Handle session tokens (older format)
        if (accessToken && refreshToken) {
          try {
            const { error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (sessionError) {
              toast.error('Authentication failed. Please try again.');
              navigate('/sign-in');
              return;
            }


            // Check if this is for password reset
            if (type === 'recovery') {
              navigate('/reset-password');
              return;
            }

            // Default redirect to home
            navigate('/');
            return;
          } catch (err) {
            toast.error('Authentication failed. Please try again.');
            navigate('/sign-in');
            return;
          }
        }

        // Special case: if we have any token but no type, assume it's a recovery
        if (token && !type) {
          try {
            const { data, error: verifyError } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'recovery'
            });

            if (verifyError) {
              toast.error('Invalid or expired reset link. Please request a new password reset.');
              navigate('/forgot-password');
              return;
            }

            navigate('/reset-password');
            return;
          } catch (err) {
            toast.error('Invalid reset link. Please request a new password reset.');
            navigate('/forgot-password');
            return;
          }
        }

        // If we reach here, we don't have the expected parameters
        toast.error('Invalid authentication link. Please try again.');
        navigate('/sign-in');

      } catch (error) {
        toast.error('Authentication failed. Please try again.');
        navigate('/sign-in');
      }
    };

    handleAuthCallback();
  }, [navigate]); // Remove searchParams dependency since we're reading from window.location directly

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md mx-auto text-center space-y-6"
      >
        <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Loader2 className="w-8 h-8 text-purple-400 animate-spin" />
        </div>
        <h1 className="text-2xl font-semibold text-white mb-2">Processing...</h1>
        <p className="text-gray-400 text-sm">
          Please wait while we process your authentication request.
        </p>
      </motion.div>
    </div>
  );
};

export default AuthCallbackPage;
