import { useAuth } from "@/contexts/AuthContext";
import { ReactNode, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import LoadingAnimation from "../LoadingAnimation";
import { isAuthRoute, isSafeRedirectPath } from "@/lib/routes";

interface AuthGuardProps {
  children: ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export const AuthGuard = ({
  children,
  redirectTo = "/sign-in",
  requireAuth = true
}: AuthGuardProps) => {
  const { user, loading, setRedirectAfterAuth } = useAuth();
  const location = useLocation();

  useEffect(() => {
    // If user is not authenticated and we require auth, store the current path for redirect
    if (!loading && !user && requireAuth) {
      const currentPath = location.pathname + location.search;

      // Only store safe, non-auth paths as redirect destinations
      if (!isAuthRoute(location.pathname) && isSafeRedirectPath(currentPath)) {
        setRedirectAfterAuth(currentPath);
      }
    }
  }, [user, loading, location, setRedirectAfterAuth, requireAuth]);

  // Show loading while auth state is being determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#111111]">
        <LoadingAnimation />
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !user) {
    return <Navigate to={redirectTo} replace />;
  }

  // If authentication is not required but user is authenticated (for auth pages)
  if (!requireAuth && user) {
    return <Navigate to="/" replace />;
  }

  // Render children if authentication requirements are met
  return <>{children}</>;
};

export default AuthGuard;
