import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Mail } from "lucide-react";
import { Link } from "react-router-dom";
import { validateResetPasswordForm, FormErrors } from "@/lib/auth";

export const ForgotPasswordForm = () => {
  const { resetPassword, loading } = useAuth();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isEmailSent, setIsEmailSent] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const formErrors = validateResetPasswordForm(email);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await resetPassword(email);
      
      if (!error) {
        setIsEmailSent(true);
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isEmailSent) {
    return (
      <div className="w-full">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-8 h-8 text-green-400" />
            </div>
            <h1 className="text-2xl font-semibold text-white mb-3">Check your email</h1>
            <p className="text-gray-400 text-sm mb-2">
              We've sent a password reset link to
            </p>
            <p className="text-purple-400 font-medium mb-4">{email}</p>
            <p className="text-gray-400 text-sm">
              Click the link in your email to reset your password.
            </p>
            <div className="mt-6 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <p className="text-yellow-400 text-sm">
                <strong>Note:</strong> Check your spam/junk folder if you don't see it within 5 minutes.
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <Link to="/sign-in">
              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium h-11 rounded-lg transition-colors duration-200">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Sign In
              </Button>
            </Link>

            <Button
              onClick={() => setIsEmailSent(false)}
              variant="outline"
              className="w-full bg-[#111111] border border-gray-700 text-white hover:bg-[#2a2a2a] hover:border-purple-400 transition-colors duration-200 h-11"
            >
              Try different email
            </Button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
      >
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-white mb-3">Reset Password</h1>
          <p className="text-gray-400 text-sm">
            Enter your email address and we'll send you a secure link to reset your password.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11"
              placeholder="Enter your email address"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white font-medium h-11 rounded-lg transition-colors duration-200"
          >
            {isSubmitting ? "Sending reset link..." : "Send Reset Link"}
          </Button>
        </form>

        <div className="text-center">
          <Link
            to="/sign-in"
            className="text-gray-400 hover:text-purple-400 text-sm hover:underline inline-flex items-center transition-colors duration-200"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Sign In
          </Link>
        </div>
      </motion.div>
    </div>
  );
};

export default ForgotPasswordForm;
