import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "./ProtectedRoute";
import { motion, AnimatePresence } from "framer-motion";
import {
  Setting<PERSON>, <PERSON>, User, <PERSON>, Key, Palette, ArrowLeft, Save, Edit3,
  Mail, Calendar, MapPin, Phone, Globe, Award, Activity,
  Download, Star, Clock, CheckCircle, AlertCircle, Loader2, Eye,
  RefreshCw, Smartphone, Monitor, Trash2, ChevronLeft, ChevronRight,
  Unlock, Code, FileText, HardDrive, Search, Filter, X,
  CreditCard
} from "lucide-react";

// Plan-based limits configuration
interface PlanLimits {
  unlock_limit: number;
  readcode_limit: number;
  other_operations_limit: number | string;
}

const PLAN_LIMITS: Record<string, PlanLimits> = {
  'basic': {
    unlock_limit: 50,
    readcode_limit: 0,
    other_operations_limit: 250
  },
  'plus': {
    unlock_limit: 100,
    readcode_limit: 0,
    other_operations_limit: 500
  },
  'pro': {
    unlock_limit: 250,
    readcode_limit: 0,
    other_operations_limit: 1500
  },
  'max': {
    unlock_limit: 500,
    readcode_limit: 100,
    other_operations_limit: 2500
  },
  'max pro': {
    unlock_limit: 1000,
    readcode_limit: 200,
    other_operations_limit: "Unlimited"
  },
  'max pro plus': {
    unlock_limit: 1500,
    readcode_limit: 300,
    other_operations_limit: "Unlimited"
  }
};

// Function to get plan limits based on user's plan name
const getPlanLimits = (planName: string | null): PlanLimits => {
  if (!planName) {
    // Default limits for users without a plan
    return {
      unlock_limit: 0,
      readcode_limit: 0,
      other_operations_limit: 0
    };
  }

  const lowerPlanName = planName.toLowerCase().trim();

  // Check for exact matches first, then partial matches
  if (lowerPlanName.includes('max pro plus')) {
    return PLAN_LIMITS['max pro plus'];
  } else if (lowerPlanName.includes('max pro')) {
    return PLAN_LIMITS['max pro'];
  } else if (lowerPlanName.includes('max')) {
    return PLAN_LIMITS['max'];
  } else if (lowerPlanName.includes('pro')) {
    return PLAN_LIMITS['pro'];
  } else if (lowerPlanName.includes('plus')) {
    return PLAN_LIMITS['plus'];
  } else if (lowerPlanName.includes('basic')) {
    return PLAN_LIMITS['basic'];
  }

  // Default to basic plan limits if no match found
  return PLAN_LIMITS['basic'];
};

// Function to format limit display
const formatLimitDisplay = (used: number, limit: number | string): string => {
  if (limit === "Unlimited") {
    return "Unlimited";
  }
  return `${used}/${limit}`;
};

// Function to calculate progress percentage
const calculateProgress = (used: number, limit: number | string): number => {
  if (limit === "Unlimited") {
    return 0; // No progress bar for unlimited
  }
  const numericLimit = typeof limit === 'string' ? parseInt(limit) : limit;
  return Math.min(100, (used / Math.max(1, numericLimit)) * 100);
};

// Function to generate realistic mock usage data based on plan limits
const generateMockUsage = (planLimits: PlanLimits) => {
  const unlockUsage = planLimits.unlock_limit === 0 ? 0 :
    Math.floor(Math.random() * Math.min(planLimits.unlock_limit as number, planLimits.unlock_limit as number * 0.8));

  const readcodeUsage = planLimits.readcode_limit === 0 ? 0 :
    Math.floor(Math.random() * Math.min(planLimits.readcode_limit as number, planLimits.readcode_limit as number * 0.6));

  const otherUsage = planLimits.other_operations_limit === "Unlimited" ?
    Math.floor(Math.random() * 2000) :
    Math.floor(Math.random() * Math.min(planLimits.other_operations_limit as number, planLimits.other_operations_limit as number * 0.7));

  return {
    unlockOperationsUsed: unlockUsage,
    readcodeOperationsUsed: readcodeUsage,
    otherOperationsUsed: otherUsage
  };
};

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link } from "react-router-dom";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { toast } from "sonner";
import { useUserProfile } from "@/hooks/useUserProfile";
import { supabase } from "@/integrations/supabase/client";
import QRCode from "qrcode";
import { PasswordChangeForm } from "./PasswordChangeForm";
import TrialStatus from "@/components/TrialStatus";

// Interfaces for new functionality
interface UserSession {
  id: string;
  device: string;
  browser: string;
  location: string;
  lastActive: string;
  isCurrent: boolean;
}

interface OperationDetail {
  operation_id: string;
  operation_type: string;
  time: string;
  status: string;
  model?: string;
  brand?: string;
  android?: string;
  baseband?: string;
  carrier?: string;
  credit?: string;
  hwid?: string;
  imei?: string;
  phone_sn?: string;
  security_patch?: string;
  username?: string;
}

interface CertFile {
  Imei: string;
  Phone_sn: string;
  Model?: string;
  Email?: string;
  Hwid?: string;
  ImeiSign?: string;
  PubKey?: string;
  PubKeySign?: string;
  Notes?: string;
  uid: string;
}

interface BackupFile {
  id: string;
  user_id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  model?: string;
  imei?: string;
  phone_sn?: string;
  upload_date?: string;
  description?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export const NewUserProfilePage = () => {
  const { user, updateProfile } = useAuth();
  const { profileData, userStats, trialInfo, loading: profileLoading, error, updateUserProfile } = useUserProfile(user);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  // 2FA State
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
  const [twoFactorSecret, setTwoFactorSecret] = useState<string>("");
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [showQrCode, setShowQrCode] = useState(false);

  // Activity pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [allOperations, setAllOperations] = useState<OperationDetail[]>([]);
  const [totalOperations, setTotalOperations] = useState(0);
  const [selectedOperation, setSelectedOperation] = useState<OperationDetail | null>(null);
  const [showOperationDetails, setShowOperationDetails] = useState(false);

  // Cert files state
  const [certFiles, setCertFiles] = useState<CertFile[]>([]);
  const [totalCertFiles, setTotalCertFiles] = useState(0);
  const [certCurrentPage, setCertCurrentPage] = useState(1);

  // Backup files state
  const [backupFiles, setBackupFiles] = useState<BackupFile[]>([]);
  const [totalBackupFiles, setTotalBackupFiles] = useState(0);
  const [backupCurrentPage, setBackupCurrentPage] = useState(1);

  // Loading states
  const [certFilesLoading, setCertFilesLoading] = useState(false);
  const [backupFilesLoading, setBackupFilesLoading] = useState(false);
  const [downloadingCert, setDownloadingCert] = useState<string | null>(null);
  const [downloadingBackup, setDownloadingBackup] = useState<string | null>(null);

  // Search and filter states
  const [certSearchTerm, setCertSearchTerm] = useState('');
  const [certModelFilter, setCertModelFilter] = useState('');
  const [backupSearchTerm, setBackupSearchTerm] = useState('');
  const [backupModelFilter, setBackupModelFilter] = useState('');
  const [activitySearchTerm, setActivitySearchTerm] = useState('');
  const [activityTypeFilter, setActivityTypeFilter] = useState('');
  const [activityStatusFilter, setActivityStatusFilter] = useState('');

  // Sub-tab state for My Backups
  const [backupSubTab, setBackupSubTab] = useState<'certificates' | 'files'>('certificates');

  // Sessions state
  const [userSessions, setUserSessions] = useState<UserSession[]>([]);
  const [sessionsLoading, setSessionsLoading] = useState(false);

  // HWID Reset state
  const [showHwidResetDialog, setShowHwidResetDialog] = useState(false);
  const [hwidResetLoading, setHwidResetLoading] = useState(false);

  // Password Change state
  const [showPasswordChange, setShowPasswordChange] = useState(false);

  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    phone: "",
    location: "",
    bio: "",
  });

  // Helper function to get progress bar color based on usage percentage
  const getProgressBarColor = (used: number, total: number): string => {
    const percentage = (used / total) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Initialize form data when user and profile data are loaded
  useEffect(() => {
    if (user && profileData) {
      setFormData({
        full_name: profileData.name || getUserDisplayName(user),
        email: user.email || "",
        phone: profileData.phone || user.user_metadata?.phone || "",
        location: profileData.country || user.user_metadata?.location || "",
        bio: user.user_metadata?.bio || "",
      });
      setTwoFactorEnabled(profileData.two_factor_enabled || false);
    }
  }, [user, profileData]);

  // Simple and working Base32 encode
  const base32Encode = (buffer: Uint8Array): string => {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let bits = 0;
    let value = 0;
    let output = '';

    for (let i = 0; i < buffer.length; i++) {
      value = (value << 8) | buffer[i];
      bits += 8;

      while (bits >= 5) {
        output += alphabet[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }

    if (bits > 0) {
      output += alphabet[(value << (5 - bits)) & 31];
    }

    return output;
  };

  // Simple and working Base32 decode
  const base32Decode = (encoded: string): Uint8Array => {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    const cleanInput = encoded.toUpperCase().replace(/[^A-Z2-7]/g, '');

    let bits = 0;
    let value = 0;
    const output = [];

    for (let i = 0; i < cleanInput.length; i++) {
      const index = alphabet.indexOf(cleanInput[i]);
      if (index === -1) continue;

      value = (value << 5) | index;
      bits += 5;

      if (bits >= 8) {
        output.push((value >>> (bits - 8)) & 255);
        bits -= 8;
      }
    }

    return new Uint8Array(output);
  };

  // Get correct UTC time regardless of system settings
  const getCorrectUnixTime = async (): Promise<number> => {
    try {
      // Try to get time from a reliable source
      const response = await fetch('https://worldtimeapi.org/api/timezone/UTC', {
        method: 'GET',
        cache: 'no-cache'
      });

      if (response.ok) {
        const data = await response.json();
        return Math.floor(new Date(data.datetime).getTime() / 1000);
      }
    } catch (error) {
      // Fallback to local time if server is unavailable
    }

    // Fallback to local time
    return Math.floor(Date.now() / 1000);
  };

  // TOTP implementation with correct time synchronization
  const generateTOTP = async (secret: string, customTimeStep?: number): Promise<string> => {
    // Decode the base32 secret
    const key = base32Decode(secret);

    let timeStep: number;

    if (customTimeStep !== undefined) {
      timeStep = customTimeStep;
    } else {
      // Get correct UTC time
      const correctUnixTime = await getCorrectUnixTime();
      timeStep = Math.floor(correctUnixTime / 30);
    }



    // Convert time step to 8-byte array (big-endian)
    const timeBytes = new ArrayBuffer(8);
    const timeView = new DataView(timeBytes);
    timeView.setUint32(0, 0, false); // High 32 bits = 0
    timeView.setUint32(4, timeStep, false); // Low 32 bits = time step

    // Create proper ArrayBuffer for the key
    const keyBuffer = new ArrayBuffer(key.length);
    const keyView = new Uint8Array(keyBuffer);
    keyView.set(key);

    // Import key for HMAC-SHA1
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'HMAC', hash: 'SHA-1' },
      false,
      ['sign']
    );

    // Generate HMAC-SHA1
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, timeBytes);
    const hmac = new Uint8Array(signature);

    // Dynamic truncation
    const offset = hmac[19] & 0x0f;
    const code = (
      ((hmac[offset] & 0x7f) << 24) |
      (hmac[offset + 1] << 16) |
      (hmac[offset + 2] << 8) |
      hmac[offset + 3]
    ) % 1000000;

    return code.toString().padStart(6, '0');
  };

  // Verify TOTP code
  const verifyTOTP = async (secret: string, token: string): Promise<boolean> => {
    // Get correct time step using the same method as generateTOTP
    const correctUnixTime = await getCorrectUnixTime();
    const currentTimeStep = Math.floor(correctUnixTime / 30);

    // Check current time window and ±1 window for clock drift
    for (let i = -1; i <= 1; i++) {
      const expectedToken = await generateTOTP(secret, currentTimeStep + i);
      if (expectedToken === token) {
        return true;
      }
    }

    return false;
  };

  // Generate 2FA secret and QR code - FIXED VERSION
  const generateTwoFactorSecret = async () => {
    try {
      // Generate a random 10-byte secret (80 bits) - more compatible
      const randomBytes = crypto.getRandomValues(new Uint8Array(10));
      const secret = base32Encode(randomBytes);

      const serviceName = "Pegasus Tools";
      const accountName = user?.email || "user";

      // Create otpauth URL
      const otpAuthUrl = `otpauth://totp/${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(serviceName)}`;

      const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl, {
        errorCorrectionLevel: 'M',
        margin: 2,
        width: 256,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      setTwoFactorSecret(secret);
      setQrCodeUrl(qrCodeDataUrl);
      setShowQrCode(true);

    } catch (error) {
      toast.error("Failed to generate 2FA secret");
    }
  };

  // Verify and enable 2FA
  const verifyAndEnable2FA = async () => {
    if (!verificationCode || !twoFactorSecret) {
      toast.error("Please enter the verification code");
      return;
    }

    if (verificationCode.length !== 6 || !/^\d{6}$/.test(verificationCode)) {
      toast.error("Please enter a valid 6-digit code");
      return;
    }

    try {
      setIsLoading(true);

      // Verify the TOTP code
      const isValid = await verifyTOTP(twoFactorSecret, verificationCode);

      if (!isValid) {
        toast.error("Invalid verification code. Please try again.");
        return;
      }

      // Store the secret in the database
      const { error } = await updateUserProfile({
        otp_secret: twoFactorSecret,
        two_factor_enabled: true,
      });

      if (error) {
        toast.error("Failed to enable 2FA");
        return;
      }

      setTwoFactorEnabled(true);
      setShowQrCode(false);
      setVerificationCode("");
      toast.success("Two-factor authentication enabled successfully!");
    } catch (error) {
      toast.error("Failed to enable 2FA");
    } finally {
      setIsLoading(false);
    }
  };





  // Disable 2FA
  const disable2FA = async () => {
    try {
      setIsLoading(true);

      const { error } = await updateUserProfile({
        otp_secret: null,
        two_factor_enabled: false,
      });

      if (error) {
        toast.error("Failed to disable 2FA");
        return;
      }

      setTwoFactorEnabled(false);
      setTwoFactorSecret("");
      setQrCodeUrl("");
      toast.success("Two-factor authentication disabled");
    } catch (error) {
      toast.error("Failed to disable 2FA");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle password change success
  const handlePasswordChangeSuccess = () => {
    setShowPasswordChange(false);
  };

  // Handle password change cancel
  const handlePasswordChangeCancel = () => {
    setShowPasswordChange(false);
  };

  // Reset HWID with 15-day deduction
  const resetHwid = async () => {
    if (!profileData) return;
  
    try {
      setHwidResetLoading(true);
  
      // Calculate new expiry date (subtract 15 days)
      let newExpiryTime = null;
      if (profileData.expiry_time) {
        const currentExpiry = new Date(profileData.expiry_time);
        currentExpiry.setDate(currentExpiry.getDate() - 5);
  
        // ✅ فقط التاريخ بدون الوقت
        newExpiryTime = currentExpiry.toISOString().split("T")[0];
      }
  
      const { error } = await updateUserProfile({
        hwid: 'Null',
        expiry_time: newExpiryTime,  // ← الآن يتم حفظه كـ "2025-07-22"
      });
  
      if (error) {
        toast.error("Failed to reset HWID");
        return;
      }
  
      setShowHwidResetDialog(false);
      toast.success("HWID reset successfully. 15 days deducted from account expiration.");
    } catch (error) {
      toast.error("Failed to reset HWID");
    } finally {
      setHwidResetLoading(false);
    }
  };
  

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Update the users table
      const { error: profileError } = await updateUserProfile({
        name: formData.full_name,
        phone: formData.phone,
        country: formData.location,
      });

      if (profileError) {
        toast.error(profileError);
        return;
      }

      // Update auth user metadata
      const { error: authError } = await updateProfile({
        data: {
          full_name: formData.full_name,
          phone: formData.phone,
          location: formData.location,
          bio: formData.bio,
        }
      });

      if (!authError) {
        setIsEditing(false);
        toast.success("Profile updated successfully!");
      } else {
        toast.error("Failed to update auth profile");
      }
    } catch (err) {
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch all operations with pagination
  const fetchAllOperations = async () => {
    if (!user) return;

    try {
      const { data: operations, error } = await supabase
        .from('operations')
        .select('*')
        .eq('uid', user.id)
        .order('time', { ascending: false });

      if (error) throw error;

      setAllOperations(operations || []);
      setTotalOperations(operations?.length || 0);
    } catch (error) {
      toast.error("Failed to fetch operations");
    }
  };

  // Fetch all cert files
  const fetchAllCertFiles = async () => {
    if (!user) return;

    try {
      setCertFilesLoading(true);
      const { data: certFiles, error } = await supabase
        .from('certsave')
        .select('*')
        .eq('uid', user.id)
        .order('Imei', { ascending: false });

      if (error) throw error;

      setCertFiles(certFiles || []);
      setTotalCertFiles(certFiles?.length || 0);
    } catch (error) {
      toast.error("Failed to fetch cert files");
    } finally {
      setCertFilesLoading(false);
    }
  };

  // Fetch all backup files
  const fetchAllBackupFiles = async () => {
    if (!user || !profileData) return;

    try {
      setBackupFilesLoading(true);

      // Use profileData.id if available, otherwise try to fetch from users table
      let userId = profileData.id;

      if (!userId) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('email', user.email)
          .single();

        if (userError) {
          // Don't show error toast for new users who don't have backup files yet
          return;
        }

        userId = userData.id;
      }

      // Now fetch backup files using the correct user_id
      const { data: backupFiles, error } = await supabase
        .from('mybackups' as any)
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true) // Only fetch active backups
        .order('created_at', { ascending: false });

      if (error) {
        toast.error("Failed to fetch backup files");
        return;
      }

      // Map the data to our BackupFile interface (data should already match our interface)
      const mappedBackups: BackupFile[] = (backupFiles || []).map((backup: any) => ({
        id: backup.id,
        user_id: backup.user_id,
        file_name: backup.file_name,
        file_path: backup.file_path,
        file_size: backup.file_size,
        model: backup.model,
        imei: backup.imei,
        phone_sn: backup.phone_sn,
        upload_date: backup.upload_date,
        description: backup.description,
        is_active: backup.is_active,
        created_at: backup.created_at,
        updated_at: backup.updated_at
      }));

      setBackupFiles(mappedBackups);
      setTotalBackupFiles(mappedBackups.length);

    } catch (error) {
      toast.error("Failed to fetch backup files");
    } finally {
      setBackupFilesLoading(false);
    }
  };

  // Get paginated operations (with filtering)
  const getPaginatedOperations = () => {
    const filtered = getFilteredOperations();
    const startIndex = (currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    return filtered.slice(startIndex, endIndex);
  };

  // Get total pages (with filtering)
  const getTotalPages = () => {
    const filtered = getFilteredOperations();
    return Math.ceil(filtered.length / 5);
  };

  // Get paginated cert files (with filtering)
  const getPaginatedCertFiles = () => {
    const filtered = getFilteredCertFiles();
    const startIndex = (certCurrentPage - 1) * 5;
    const endIndex = startIndex + 5;
    return filtered.slice(startIndex, endIndex);
  };

  // Get total cert pages (with filtering)
  const getTotalCertPages = () => {
    const filtered = getFilteredCertFiles();
    return Math.ceil(filtered.length / 5);
  };

  // Get paginated backup files (with filtering)
  const getPaginatedBackupFiles = () => {
    const filtered = getFilteredBackupFiles();
    const startIndex = (backupCurrentPage - 1) * 5;
    const endIndex = startIndex + 5;
    return filtered.slice(startIndex, endIndex);
  };

  // Get total backup pages (with filtering)
  const getTotalBackupPages = () => {
    const filtered = getFilteredBackupFiles();
    return Math.ceil(filtered.length / 5);
  };

  // Filter and search functions
  const getFilteredCertFiles = () => {
    return certFiles.filter(file => {
      const matchesSearch = !certSearchTerm ||
        file.Imei.toLowerCase().includes(certSearchTerm.toLowerCase()) ||
        file.Phone_sn.toLowerCase().includes(certSearchTerm.toLowerCase()) ||
        (file.Email && file.Email.toLowerCase().includes(certSearchTerm.toLowerCase()));

      const matchesModel = !certModelFilter ||
        (file.Model && file.Model.toLowerCase().includes(certModelFilter.toLowerCase()));

      return matchesSearch && matchesModel;
    });
  };

  const getFilteredBackupFiles = () => {
    return backupFiles.filter(file => {
      const matchesSearch = !backupSearchTerm ||
        file.file_name.toLowerCase().includes(backupSearchTerm.toLowerCase()) ||
        (file.description && file.description.toLowerCase().includes(backupSearchTerm.toLowerCase())) ||
        (file.imei && file.imei.toLowerCase().includes(backupSearchTerm.toLowerCase()));

      const matchesModel = !backupModelFilter ||
        (file.model && file.model.toLowerCase().includes(backupModelFilter.toLowerCase()));

      return matchesSearch && matchesModel;
    });
  };

  const getFilteredOperations = () => {
    return allOperations.filter(operation => {
      const matchesSearch = !activitySearchTerm ||
        operation.operation_type.toLowerCase().includes(activitySearchTerm.toLowerCase()) ||
        (operation.model && operation.model.toLowerCase().includes(activitySearchTerm.toLowerCase())) ||
        (operation.imei && operation.imei.toLowerCase().includes(activitySearchTerm.toLowerCase()));

      const matchesType = !activityTypeFilter ||
        operation.operation_type.toLowerCase().includes(activityTypeFilter.toLowerCase());

      const matchesStatus = !activityStatusFilter ||
        (operation.status && operation.status.toLowerCase() === activityStatusFilter.toLowerCase());

      return matchesSearch && matchesType && matchesStatus;
    });
  };

  // Get unique values for filters
  const getUniqueModels = (files: CertFile[]) => {
    return [...new Set(files.map(file => file.Model).filter(Boolean))];
  };

  const getUniqueBackupModels = (files: BackupFile[]) => {
    return [...new Set(files.map(file => file.model).filter(Boolean))];
  };

  const getUniqueOperationTypes = (operations: OperationDetail[]) => {
    return [...new Set(operations.map(op => op.operation_type))];
  };

  const getUniqueStatuses = (operations: OperationDetail[]) => {
    return [...new Set(operations.map(op => op.status).filter(Boolean))];
  };

  // Handle cert file download
  const handleDownloadCert = async (certFile: CertFile) => {
    try {
      setDownloadingCert(certFile.Imei);

      // Format data according to the requested format
      const exportData = `[${certFile.Notes || ''}]\nIMEI=${certFile.Imei || ''}\nImeiSign=${certFile.ImeiSign || ''}\nPubKey=${certFile.PubKey || ''}\nPubKeySign=${certFile.PubKeySign || ''}`;

      // Create file name using the format: {Model}_{IMEI}_{PhoneSN}.Cert
      const model = certFile.Model || 'Unknown';
      const imei = certFile.Imei || 'Unknown';
      const phoneSn = certFile.Phone_sn || 'Unknown';
      const fileName = `${model}_${imei}_${phoneSn}.Cert`;

      // Create a blob with the data
      const blob = new Blob([exportData], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);

      // Create a link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Certificate file downloaded successfully");
    } catch (error) {
      toast.error("Failed to download certificate file");
    } finally {
      setDownloadingCert(null);
    }
  };

  // Handle backup file download
  const handleDownloadBackup = async (backupFile: BackupFile) => {
    try {
      setDownloadingBackup(backupFile.id);

      // Get the file from Supabase Storage bucket 'user-backups'
      const { data, error } = await supabase.storage
        .from('user-backups')
        .download(backupFile.file_path);

      if (error) {
        toast.error("Failed to download backup file");
        return;
      }

      // Create a download link
      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = backupFile.file_name;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Backup file downloaded successfully");
    } catch (error) {
      toast.error("Failed to download backup file");
    } finally {
      setDownloadingBackup(null);
    }
  };

  // Fetch user sessions (mock implementation - replace with real session tracking)
  const fetchUserSessions = async () => {
    setSessionsLoading(true);
    try {
      // This is a mock implementation. In a real app, you'd fetch from a sessions table
      // For now, we'll create some realistic mock data based on the current user
      const mockSessions: UserSession[] = [
        {
          id: "current",
          device: "Windows PC",
          browser: "Chrome",
          location: "Current Location",
          lastActive: "Now",
          isCurrent: true,
        },
        // Add more sessions based on actual login history if available
      ];

      setUserSessions(mockSessions);
    } catch (error) {
      toast.error("Failed to fetch sessions");
    } finally {
      setSessionsLoading(false);
    }
  };

  // Revoke session
  const revokeSession = async (sessionId: string) => {
    try {
      // In a real implementation, you'd revoke the session from the database
      setUserSessions(prev => prev.filter(session => session.id !== sessionId));
      toast.success("Session revoked successfully");
    } catch (error) {
      toast.error("Failed to revoke session");
    }
  };

  // Load data on component mount - wait for profileData to be available
  useEffect(() => {
    if (user && profileData) {
      fetchAllOperations();
      fetchUserSessions();
      fetchAllCertFiles();
      fetchAllBackupFiles();
    }
  }, [user, profileData]);

  // Reset pagination when filters change
  useEffect(() => {
    setCertCurrentPage(1);
  }, [certSearchTerm, certModelFilter]);

  useEffect(() => {
    setBackupCurrentPage(1);
  }, [backupSearchTerm, backupModelFilter]);

  useEffect(() => {
    setCurrentPage(1);
  }, [activitySearchTerm, activityTypeFilter, activityStatusFilter]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.4 }
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'mybackups', label: 'My Backups', icon: HardDrive },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'activity', label: 'Activity', icon: Activity },
  ];

  // Show loading state
  if (!user || profileLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <Loader2 className="w-6 h-6 animate-spin text-purple-400" />
            <span className="text-lg">Loading profile...</span>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // Show error state
  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error Loading Profile</h2>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} className="bg-purple-600 hover:bg-purple-700">
              Retry
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const userName = getUserDisplayName(user);
  const userAvatar = getUserAvatar(user);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 z-0 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        {/* Animated particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-purple-500/5"
            style={{
              width: Math.random() * 100 + 50,
              height: Math.random() * 100 + 50,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -200, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              delay: Math.random() * 10,
            }}
          />
        ))}

        <div className="relative z-10 py-8 px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="max-w-7xl mx-auto space-y-8"
          >
            {/* Header */}
            <motion.div variants={itemVariants} className="mb-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold text-white mb-2">Profile Dashboard</h1>
                    <p className="text-gray-400">Manage your account settings and preferences</p>
                  </div>
                </div>


              </div>
            </motion.div>
            {/* Unified Profile Header with Operation Limits */}
            <motion.div variants={itemVariants} className="mb-12">
              <div className="bg-gradient-to-r from-purple-600/20 via-purple-500/10 to-purple-700/20 rounded-2xl p-6 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

                <div className="relative z-10 space-y-6">
                  {/* User Profile Section */}
                  <div className="flex flex-col lg:flex-row items-center gap-6">
                    <div className="relative">
                      <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-purple-500/30 shadow-2xl">
                        {userAvatar ? (
                          <img
                            src={userAvatar}
                            alt="User Avatar"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-2xl">
                            {userName.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-[#111111] flex items-center justify-center">
                        <CheckCircle className="w-3 h-3 text-white" />
                      </div>
                    </div>

                    <div className="flex-1 text-center lg:text-left">
                      <h2 className="text-2xl font-bold text-white mb-1">{userName}</h2>
                      <p className="text-purple-400 text-base mb-3">{user.email}</p>
                      <div className="flex flex-wrap items-center justify-center lg:justify-start gap-3">
                        <div className="flex items-center gap-2 bg-[#1a1a1a] px-2 py-1 rounded-full border border-gray-700/50">
                          <div className={`w-2 h-2 rounded-full ${
                            userStats?.accountStatus === 'active' ? 'bg-green-500 animate-pulse' :
                            userStats?.accountStatus === 'blocked' ? 'bg-red-500' : 'bg-yellow-500'
                          }`}></div>
                          <span className={`text-xs capitalize ${
                            userStats?.accountStatus === 'active' ? 'text-green-400' :
                            userStats?.accountStatus === 'blocked' ? 'text-red-400' : 'text-yellow-400'
                          }`}>
                            {userStats?.accountStatus || 'Loading...'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 bg-[#1a1a1a] px-2 py-1 rounded-full border border-gray-700/50">
                          <Calendar className="w-3 h-3 text-purple-400" />
                          <span className="text-gray-300 text-xs">Joined {userStats?.joinDate || 'Loading...'}</span>
                        </div>
                        <div className="flex items-center gap-2 bg-[#1a1a1a] px-2 py-1 rounded-full border border-gray-700/50">
                          <Clock className="w-3 h-3 text-blue-400" />
                          <span className="text-gray-300 text-xs">Last login {userStats?.lastLogin || 'Loading...'}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Trial Message for Active Trial Users */}
                  {profileData && trialInfo && trialInfo.isTrialActive && (
                    <div className="bg-gradient-to-r from-blue-600/15 to-purple-600/15 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                          <Star className="w-5 h-5 text-blue-400" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Free Trial Active</h3>
                          <p className="text-blue-300 text-sm">Unlimited access to all features</p>
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm">
                        You're currently enjoying unlimited operations during your free trial period.
                        No limits apply until your trial expires.
                      </p>
                    </div>
                  )}

                  {/* Operation Limits Cards - Plan-based limits (show when trial is not active) */}
                  {profileData && trialInfo && !trialInfo.isTrialActive && (() => {
                    const planLimits = getPlanLimits(profileData.my_plans);

                    // Check if user has expired trial, active subscription, or credits license
                    const isTrialExpired = trialInfo.subscriptionStatus === 'expired' ||
                                          (profileData.user_type === 'expired');

                    const isCreditsUser = profileData.user_type === 'Credits License' ||
                                         profileData.my_plans === 'Credit';

                    // For expired trial users or credits users, show zero limits (they use credits instead)
                    const effectiveLimits = (isTrialExpired || isCreditsUser) ? {
                      unlock_limit: 0,
                      readcode_limit: 0,
                      other_operations_limit: 0
                    } : planLimits;

                    // Calculate actual usage from database values
                    const actualUsage = (() => {
                      if (isTrialExpired) {
                        return {
                          unlockOperationsUsed: 0,
                          readcodeOperationsUsed: 0,
                          otherOperationsUsed: 0
                        };
                      }

                      // Calculate used operations from current limits vs plan limits
                      const planUnlockLimit = planLimits.unlock_limit as number;
                      const planReadcodeLimit = planLimits.readcode_limit as number;
                      const planOtherLimit = planLimits.other_operations_limit as number;

                      const currentUnlockLimit = profileData.unlock_limit || 0;
                      const currentReadcodeLimit = profileData.readcode_limit || 0;
                      const currentOtherLimit = parseInt(profileData.other_operations_limit || '0');

                      return {
                        unlockOperationsUsed: Math.max(0, planUnlockLimit - currentUnlockLimit),
                        readcodeOperationsUsed: Math.max(0, planReadcodeLimit - currentReadcodeLimit),
                        otherOperationsUsed: planLimits.other_operations_limit === "Unlimited"
                          ? 0
                          : Math.max(0, planOtherLimit - currentOtherLimit)
                      };
                    })();

                    return (
                      <div className="space-y-3">
                        {/* Show appropriate message based on user status */}
                        {isCreditsUser && (
                          <div className="bg-gradient-to-r from-blue-600/15 to-cyan-600/15 border border-blue-500/30 rounded-xl p-4 backdrop-blur-sm mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                                <CreditCard className="w-4 h-4 text-blue-400" />
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold text-white">Credits System Active</h4>
                                <p className="text-blue-300 text-xs">Your account now uses credits for operations. Check your credits balance below.</p>
                              </div>
                            </div>
                          </div>
                        )}
                        {isTrialExpired && !isCreditsUser && (
                          <div className="bg-gradient-to-r from-red-600/15 to-orange-600/15 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                                <AlertCircle className="w-4 h-4 text-red-400" />
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold text-white">Free Trial Expired</h4>
                                <p className="text-red-300 text-xs">Your account now has limited access. Upgrade to restore full functionality.</p>
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="flex items-center gap-2 mb-2">
                          <Settings className="w-4 h-4 text-purple-400" />
                          <h3 className="text-sm font-semibold text-white">Account Overview</h3>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            isCreditsUser
                              ? 'text-blue-400 bg-blue-800/30'
                              : isTrialExpired
                                ? 'text-red-400 bg-red-800/30'
                                : 'text-gray-400 bg-gray-800/50'
                          }`}>
                            {isCreditsUser ? 'Credits System' :
                             isTrialExpired ? 'Trial Expired' :
                             (profileData.my_plans || 'No Plan')}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
                          {/* Unlock Operations */}
                          <div className="bg-gradient-to-r from-green-600/15 to-green-500/10 border border-green-500/20 rounded-lg p-3 backdrop-blur-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                                  <Unlock className="w-4 h-4 text-green-400" />
                                </div>
                                <div>
                                  <p className="text-gray-400 text-xs">Unlock</p>
                                  <p className="text-white font-bold text-sm">
                                    {isCreditsUser ? "Uses Credits" :
                                     isTrialExpired ? "0/0" :
                                     `${profileData.unlock_limit || 0}/${effectiveLimits.unlock_limit}`}
                                  </p>
                                </div>
                              </div>
                              {typeof effectiveLimits.unlock_limit === 'number' && effectiveLimits.unlock_limit > 0 && (
                                <div className="w-full bg-gray-700/50 rounded-full h-1.5">
                                  <div
                                    className={`h-1.5 rounded-full transition-all duration-500 ${getProgressBarColor(actualUsage.unlockOperationsUsed, effectiveLimits.unlock_limit as number)}`}
                                    style={{
                                      width: `${calculateProgress(actualUsage.unlockOperationsUsed, effectiveLimits.unlock_limit)}%`
                                    }}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Read Code Operations */}
                          <div className="bg-gradient-to-r from-cyan-600/15 to-cyan-500/10 border border-cyan-500/20 rounded-lg p-3 backdrop-blur-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-cyan-500/20 rounded-full flex items-center justify-center">
                                  <Code className="w-4 h-4 text-cyan-400" />
                                </div>
                                <div>
                                  <p className="text-gray-400 text-xs">Read Code</p>
                                  <p className="text-white font-bold text-sm">
                                    {isCreditsUser ? "Uses Credits" :
                                     effectiveLimits.readcode_limit === 0 ? "Not Available" :
                                     isTrialExpired ? "0/0" : `${profileData.readcode_limit || 0}/${effectiveLimits.readcode_limit}`}
                                  </p>
                                </div>
                              </div>
                              {typeof effectiveLimits.readcode_limit === 'number' && effectiveLimits.readcode_limit > 0 && (
                                <div className="w-full bg-gray-700/50 rounded-full h-1.5">
                                  <div
                                    className={`h-1.5 rounded-full transition-all duration-500 ${getProgressBarColor(actualUsage.readcodeOperationsUsed, effectiveLimits.readcode_limit as number)}`}
                                    style={{
                                      width: `${calculateProgress(actualUsage.readcodeOperationsUsed, effectiveLimits.readcode_limit)}%`
                                    }}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Other Operations */}
                          <div className="bg-gradient-to-r from-orange-600/15 to-orange-500/10 border border-orange-500/20 rounded-lg p-3 backdrop-blur-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center">
                                  <Settings className="w-4 h-4 text-orange-400" />
                                </div>
                                <div>
                                  <p className="text-gray-400 text-xs">Other</p>
                                  <p className="text-white font-bold text-sm">
                                    {isCreditsUser ? "Uses Credits" :
                                     isTrialExpired ? "0/0" :
                                     effectiveLimits.other_operations_limit === "Unlimited" ? "Unlimited" :
                                     `${profileData.other_operations_limit || 0}/${effectiveLimits.other_operations_limit}`}
                                  </p>
                                </div>
                              </div>
                              {effectiveLimits.other_operations_limit !== "Unlimited" && typeof effectiveLimits.other_operations_limit === 'number' && effectiveLimits.other_operations_limit > 0 && (
                                <div className="w-full bg-gray-700/50 rounded-full h-1.5">
                                  <div
                                    className={`h-1.5 rounded-full transition-all duration-500 ${getProgressBarColor(actualUsage.otherOperationsUsed, effectiveLimits.other_operations_limit as number)}`}
                                    style={{
                                      width: `${calculateProgress(actualUsage.otherOperationsUsed, effectiveLimits.other_operations_limit)}%`
                                    }}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Plan Info Card */}
                          <div className="bg-gradient-to-r from-purple-600/15 to-purple-500/10 border border-purple-500/20 rounded-lg p-3 backdrop-blur-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                                  <Star className="w-4 h-4 text-purple-400" />
                                </div>
                                <div>
                                  <p className="text-gray-400 text-xs">Current Plan</p>
                                  <p className="text-white font-bold text-sm">
                                    {profileData.my_plans || 'No Plan'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Upgrade Suggestion Card */}
                          <div className="bg-gradient-to-r from-yellow-600/15 to-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 backdrop-blur-sm">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                                  <Award className="w-4 h-4 text-yellow-400" />
                                </div>
                                <div>
                                  <p className="text-gray-400 text-xs">Upgrade</p>
                                  <p className="text-white font-bold text-sm">Available</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </motion.div>

            {/* Stats Cards */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">

                            <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-green-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-green-900/30 to-green-800/20 rounded-full mb-4 mx-auto">
                    <CreditCard className="h-6 w-6 text-green-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Credits</h3>
                  <p className="text-2xl font-bold text-green-400">
                    {profileData?.credits ?? <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">My Credits Balance</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-4 mx-auto">
                    <Activity className="h-6 w-6 text-purple-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Total Operations</h3>
                  <p className="text-2xl font-bold text-purple-400">
                    {userStats?.totalOperations ?? <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Device operations</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-full mb-4 mx-auto">
                    <Shield className="h-6 w-6 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Security Score</h3>
                  <p className="text-2xl font-bold text-blue-400">
                    {userStats?.securityScore ? `${userStats.securityScore}%` : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Account security</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-yellow-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-yellow-900/30 to-yellow-800/20 rounded-full mb-4 mx-auto">
                    <Star className="h-6 w-6 text-yellow-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">User Rating</h3>
                  <p className="text-2xl font-bold text-yellow-400">
                    {userStats?.userRating ? userStats.userRating.toFixed(1) : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Average rating</p>
                </div>
              </motion.div>
            </motion.div>

            {/* Tabs Navigation */}
            <motion.div variants={itemVariants} className="mb-8">
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-2 backdrop-blur-lg">
                <div className="flex flex-wrap gap-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                          activeTab === tab.id
                            ? 'bg-purple-600 text-white shadow-lg'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                        }`}
                      >
                        <Icon className="w-4 h-4" />
                        <span className="font-medium">{tab.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </motion.div>

            {/* Tab Content */}
            <AnimatePresence mode="wait">
              {activeTab === 'profile' && (
                <motion.div
                  key="profile"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg"
                >
                  {/* Trial Status */}
                  {trialInfo && (
                    <div className="mb-6">
                      <TrialStatus
                        trialInfo={trialInfo}
                        onUpgrade={() => {
                          const pricingSection = document.getElementById('pricing');
                          if (pricingSection) {
                            pricingSection.scrollIntoView({ behavior: 'smooth' });
                          }
                        }}
                      />
                    </div>
                  )}

                  {/* Profile Information */}
                    <div className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                        <User className="w-5 h-5 text-purple-400" />
                        Profile Information
                    </div>

                    <AnimatePresence>
                      {isEditing ? (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-3"
                        >
                          <div className="space-y-2">
                            <Label htmlFor="full_name" className="text-gray-300">
                              Full Name
                            </Label>
                            <Input
                              id="full_name"
                              value={formData.full_name}
                              onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your full name"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email" className="text-gray-300">
                              Email
                            </Label>
                            <Input
                              id="email"
                              value={formData.email}
                              disabled
                              className="bg-gray-800 border-gray-600 text-gray-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="phone" className="text-gray-300">
                              Phone Number
                            </Label>
                            <Input
                              id="phone"
                              value={formData.phone}
                              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your phone number"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="location" className="text-gray-300">
                              Location
                            </Label>
                            <Input
                              id="location"
                              value={formData.location}
                              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your location"
                            />
                          </div>

                          <div className="flex gap-3 pt-4">
                            <Button
                              onClick={handleSave}
                              disabled={isLoading}
                              className="bg-purple-600 hover:bg-purple-700 text-white flex-1"
                            >
                              <Save className="w-4 h-4 mr-2" />
                              {isLoading ? "Saving..." : "Save Changes"}
                            </Button>
                            <Button
                              onClick={() => setIsEditing(false)}
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-800"
                            >
                              Cancel
                            </Button>
                          </div>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="space-y-4"
                        >
                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <User className="w-5 h-5 text-purple-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Full Name</p>
                              <p className="text-white font-medium">{formData.full_name || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Mail className="w-5 h-5 text-blue-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Email</p>
                              <p className="text-white font-medium">{formData.email}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Phone className="w-5 h-5 text-green-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Phone</p>
                              <p className="text-white font-medium">{formData.phone || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <MapPin className="w-5 h-5 text-red-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Location</p>
                              <p className="text-white font-medium">{formData.location || 'Not set'}</p>
                            </div>
                          </div>

                          {/* Edit Profile Button at Bottom */}
                          <div className="pt-4 border-t border-gray-700/50">
                            <Button
                              onClick={() => setIsEditing(true)}
                              className="w-full bg-purple-600 hover:bg-purple-700 text-white transition-all duration-300 hover:scale-105"
                            >
                              <Edit3 className="w-4 h-4 mr-2" />
                              Edit Profile
                            </Button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                </motion.div>
              )}



              {activeTab === 'mybackups' && (
                <motion.div
                  key="mybackups"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg"
                >
                  <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                    <HardDrive className="w-5 h-5 text-purple-400" />
                    My Backups
                  </h3>

                  {/* Sub-tabs */}
                  <div className="flex flex-col sm:flex-row gap-4 mb-6">
                    <div className="flex bg-gray-800/30 rounded-lg p-1">
                      <button
                        onClick={() => setBackupSubTab('certificates')}
                        className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          backupSubTab === 'certificates'
                            ? 'bg-green-600 text-white shadow-lg'
                            : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                      >
                        <FileText className="w-4 h-4" />
                        Certificate Files
                      </button>
                      <button
                        onClick={() => setBackupSubTab('files')}
                        className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          backupSubTab === 'files'
                            ? 'bg-blue-600 text-white shadow-lg'
                            : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                      >
                        <HardDrive className="w-4 h-4" />
                        Security Files
                      </button>
                    </div>
                  </div>

                  {/* Certificate Files Section */}
                  {backupSubTab === 'certificates' && (
                    <div>
                      {/* Search and Filter Controls */}
                      <div className="mb-6 space-y-4">
                        <div className="flex flex-col sm:flex-row gap-4">
                          {/* Search Input */}
                          <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <input
                              type="text"
                              placeholder="Search by IMEI, Phone SN, or Email..."
                              value={certSearchTerm}
                              onChange={(e) => setCertSearchTerm(e.target.value)}
                              className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"
                            />
                            {certSearchTerm && (
                              <button
                                onClick={() => setCertSearchTerm('')}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                          </div>

                          {/* Model Filter */}
                          <div className="relative">
                            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <select
                              value={certModelFilter}
                              onChange={(e) => setCertModelFilter(e.target.value)}
                              className="pl-10 pr-8 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-500 min-w-[150px]"
                            >
                              <option value="">All Models</option>
                              {getUniqueModels(certFiles).map(model => (
                                <option key={model} value={model}>{model}</option>
                              ))}
                            </select>
                          </div>
                        </div>

                        {/* Active Filters Display */}
                        {(certSearchTerm || certModelFilter) && (
                          <div className="flex flex-wrap gap-2">
                            {certSearchTerm && (
                              <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-600/20 text-green-300 rounded-full text-sm">
                                Search: "{certSearchTerm}"
                                <button onClick={() => setCertSearchTerm('')}>
                                  <X className="w-3 h-3" />
                                </button>
                              </span>
                            )}
                            {certModelFilter && (
                              <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-600/20 text-green-300 rounded-full text-sm">
                                Model: {certModelFilter}
                                <button onClick={() => setCertModelFilter('')}>
                                  <X className="w-3 h-3" />
                                </button>
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        {certFilesLoading ? (
                          <div className="text-center py-8">
                            <Loader2 className="w-8 h-8 text-green-400 mx-auto mb-4 animate-spin" />
                            <p className="text-gray-400">Loading certificate files...</p>
                          </div>
                        ) : getPaginatedCertFiles().length > 0 ? (
                          getPaginatedCertFiles().map((certFile: CertFile, index: number) => {
                            const isDownloading = downloadingCert === certFile.Imei;
                            return (
                              <motion.div
                                key={`${certFile.Imei}-${certFile.Phone_sn}`}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800/70 transition-colors"
                              >
                                <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
                                  <FileText className="w-5 h-5 text-green-400" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-white font-medium truncate">IMEI: {certFile.Imei}</p>
                                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-sm text-gray-400">
                                    <span className="truncate">Phone SN: {certFile.Phone_sn}</span>
                                    {certFile.Model && (
                                      <>
                                        <span className="hidden sm:inline">•</span>
                                        <span className="truncate">{certFile.Model}</span>
                                      </>
                                    )}
                                    {certFile.Email && (
                                      <>
                                        <span className="hidden sm:inline">•</span>
                                        <span className="truncate">{certFile.Email}</span>
                                      </>
                                    )}
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-green-600 text-white hover:bg-green-600/10 w-full sm:w-auto flex-shrink-0"
                                  onClick={() => handleDownloadCert(certFile)}
                                  disabled={isDownloading}
                                >
                                  {isDownloading ? (
                                    <>
                                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                      Downloading...
                                    </>
                                  ) : (
                                    <>
                                      <Download className="w-4 h-4 mr-1" />
                                      Download Cert
                                    </>
                                  )}
                                </Button>
                              </motion.div>
                            );
                          })
                        ) : (
                          <div className="text-center py-8">
                            <FileText className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                            <p className="text-gray-400">No certificate files found</p>
                            <p className="text-gray-500 text-sm">Your certificate files will appear here</p>
                          </div>
                        )}
                      </div>

                      {/* Pagination Controls */}
                      {getTotalCertPages() > 1 && (
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 pt-6 border-t border-gray-700/50">
                          <div className="text-sm text-gray-400 text-center sm:text-left">
                            Showing {((certCurrentPage - 1) * 5) + 1} to {Math.min(certCurrentPage * 5, getFilteredCertFiles().length)} of {getFilteredCertFiles().length} files
                            {(certSearchTerm || certModelFilter) && (
                              <span className="text-purple-400"> (filtered from {totalCertFiles} total)</span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-800"
                              onClick={() => setCertCurrentPage(prev => Math.max(1, prev - 1))}
                              disabled={certCurrentPage === 1}
                            >
                              <ChevronLeft className="w-4 h-4" />
                              <span className="hidden sm:inline">Previous</span>
                            </Button>
                            <span className="text-sm text-gray-400 px-2 sm:px-3">
                              {certCurrentPage}/{getTotalCertPages()}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-800"
                              onClick={() => setCertCurrentPage(prev => Math.min(getTotalCertPages(), prev + 1))}
                              disabled={certCurrentPage === getTotalCertPages()}
                            >
                              <span className="hidden sm:inline">Next</span>
                              <ChevronRight className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Backup Files Section */}
                  {backupSubTab === 'files' && (
                    <div>
                      {/* Search and Filter Controls */}
                  <div className="mb-6 space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4">
                      {/* Search Input */}
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input
                          type="text"
                          placeholder="Search by filename, description, or IMEI..."
                          value={backupSearchTerm}
                          onChange={(e) => setBackupSearchTerm(e.target.value)}
                          className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                        />
                        {backupSearchTerm && (
                          <button
                            onClick={() => setBackupSearchTerm('')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      {/* Model Filter */}
                      <div className="relative">
                        <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <select
                          value={backupModelFilter}
                          onChange={(e) => setBackupModelFilter(e.target.value)}
                          className="pl-10 pr-8 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 min-w-[150px]"
                        >
                          <option value="">All Models</option>
                          {getUniqueBackupModels(backupFiles).map(model => (
                            <option key={model} value={model}>{model}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Active Filters Display */}
                    {(backupSearchTerm || backupModelFilter) && (
                      <div className="flex flex-wrap gap-2">
                        {backupSearchTerm && (
                          <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                            Search: "{backupSearchTerm}"
                            <button onClick={() => setBackupSearchTerm('')}>
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        )}
                        {backupModelFilter && (
                          <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                            Model: {backupModelFilter}
                            <button onClick={() => setBackupModelFilter('')}>
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    {backupFilesLoading ? (
                      <div className="text-center py-8">
                        <Loader2 className="w-8 h-8 text-purple-400 mx-auto mb-4 animate-spin" />
                        <p className="text-gray-400">Loading backup files...</p>
                      </div>
                    ) : getPaginatedBackupFiles().length > 0 ? (
                      getPaginatedBackupFiles().map((backupFile: BackupFile, index: number) => {
                        const isDownloading = downloadingBackup === backupFile.id;
                        return (
                          <motion.div
                            key={backupFile.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800/70 transition-colors"
                          >
                            <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
                              <HardDrive className="w-5 h-5 text-blue-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-white font-medium truncate">{backupFile.file_name}</p>
                              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-sm text-gray-400">
                                <span className="truncate">
                                  {backupFile.created_at ? new Date(backupFile.created_at).toLocaleDateString() : 'Unknown date'}
                                </span>
                                {backupFile.description && (
                                  <>
                                    <span className="hidden sm:inline">•</span>
                                    <span className="truncate">{backupFile.description}</span>
                                  </>
                                )}
                                {backupFile.model && (
                                  <>
                                    <span className="hidden sm:inline">•</span>
                                    <span className="truncate">{backupFile.model}</span>
                                  </>
                                )}
                                {backupFile.file_size && (
                                  <>
                                    <span className="hidden sm:inline">•</span>
                                    <span className="truncate">{(backupFile.file_size / 1024 / 1024).toFixed(2)} MB</span>
                                  </>
                                )}
                              </div>
                            </div>
                            
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-blue-600 text-white hover:bg-blue-600/10 w-full sm:w-auto flex-shrink-0"
                              onClick={() => handleDownloadBackup(backupFile)}
                              disabled={isDownloading}
                            >
                              {isDownloading ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  Downloading...
                                </>
                              ) : (
                                <>
                                  <Download className="w-4 h-4 mr-1" />
                                  Download
                                </>
                              )}
                            </Button>
                          </motion.div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <HardDrive className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                        <p className="text-gray-400">No backup files found</p>
                        <p className="text-gray-500 text-sm">Your backup files will appear here</p>
                      </div>
                    )}
                  </div>

                  {/* Pagination Controls */}
                  {getTotalBackupPages() > 1 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 pt-6 border-t border-gray-700/50">
                      <div className="text-sm text-gray-400 text-center sm:text-left">
                        Showing {((backupCurrentPage - 1) * 5) + 1} to {Math.min(backupCurrentPage * 5, getFilteredBackupFiles().length)} of {getFilteredBackupFiles().length} files
                        {(backupSearchTerm || backupModelFilter) && (
                          <span className="text-blue-400"> (filtered from {totalBackupFiles} total)</span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setBackupCurrentPage(prev => Math.max(1, prev - 1))}
                          disabled={backupCurrentPage === 1}
                        >
                          <ChevronLeft className="w-4 h-4" />
                          <span className="hidden sm:inline">Previous</span>
                        </Button>
                        <span className="text-sm text-gray-400 px-2 sm:px-3">
                          {backupCurrentPage}/{getTotalBackupPages()}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setBackupCurrentPage(prev => Math.min(getTotalBackupPages(), prev + 1))}
                          disabled={backupCurrentPage === getTotalBackupPages()}
                        >
                          <span className="hidden sm:inline">Next</span>
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                    </div>
                  )}
                </motion.div>
              )}

              {activeTab === 'security' && (
                <motion.div
                  key="security"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Shield className="w-5 h-5 text-purple-400" />
                      Security Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Password</p>
                          <Button
                            size="sm"
                            className="text-white bg-purple-600 hover:bg-purple-700"
                            onClick={() => setShowPasswordChange(true)}
                          >
                            Change
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Keep your account secure with a strong password</p>

                        {/* Password Change Form */}
                        {showPasswordChange && (
                          <PasswordChangeForm
                            userEmail={user?.email || ''}
                            onSuccess={handlePasswordChangeSuccess}
                            onCancel={handlePasswordChangeCancel}
                          />
                        )}
                      </div>

                      {/* Two-Factor Authentication */}
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Two-Factor Authentication</p>
                          {twoFactorEnabled ? (
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-600 text-red-400 hover:bg-red-600/10"
                              onClick={disable2FA}
                              disabled={isLoading}
                            >
                              Disable
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              className="text-white bg-green-600 hover:bg-green-700"
                              onClick={generateTwoFactorSecret}
                              disabled={isLoading}
                            >
                              Enable
                            </Button>
                          )}
                        </div>
                        <p className="text-gray-400 text-sm">
                          {twoFactorEnabled ? "2FA is currently enabled" : "Add an extra layer of security"}
                        </p>

                        {/* QR Code Display */}
                        {showQrCode && (
                          <div className="mt-4 p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                            <div className="space-y-4">
                              <div>
                                <h4 className="text-white font-medium mb-2">Setup Two-Factor Authentication</h4>
                                <p className="text-gray-400 text-sm mb-3">
                                  Follow these steps to enable 2FA:
                                </p>
                                <ol className="text-gray-300 text-sm space-y-1 mb-4">
                                  <li>1. Install an authenticator app (Google Authenticator, Authy, etc.)</li>
                                  <li>2. Scan the QR code below with your app</li>
                                  <li>3. Enter the 6-digit code from your app</li>
                                </ol>
                              </div>

                              {qrCodeUrl && (
                                <div className="flex flex-col items-center gap-4">
                                  <div className="bg-white p-4 rounded-lg">
                                    <img src={qrCodeUrl} alt="2FA QR Code" className="w-40 h-40" />
                                  </div>

                                  <div className="w-full max-w-sm">
                                    <div className="space-y-2">
                                      <Label htmlFor="verification_code" className="text-gray-300 text-sm font-medium">
                                        Verification Code
                                      </Label>
                                      <p className="text-gray-400 text-xs">
                                        Enter the 6-digit code from your authenticator app
                                      </p>
                                    </div>

                                    <div className="flex gap-2 mt-3">
                                      <Input
                                        id="verification_code"
                                        value={verificationCode}
                                        onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                                        className="bg-gray-800 border-gray-600 text-white focus:border-purple-400 text-center text-lg font-mono"
                                        placeholder="000000"
                                        maxLength={6}
                                      />
                                      <Button
                                        onClick={verifyAndEnable2FA}
                                        disabled={isLoading || verificationCode.length !== 6}
                                        className=" text-white bg-purple-600 hover:bg-purple-700 min-w-[80px]"
                                      >
                                        {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "Verify"}
                                      </Button>
                                    </div>
                                  </div>

                                  {/* Manual Secret Display */}
                                  <div className="w-full mt-4 p-3 bg-gray-800/50 rounded border border-gray-600">
                                    <p className="text-gray-400 text-xs mb-2">
                                      Can't scan? Enter this secret manually:
                                    </p>
                                    <code className="text-green-400 text-sm font-mono break-all">
                                      {twoFactorSecret}
                                    </code>
                                  </div>


                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* 2FA Status when enabled */}
                        {twoFactorEnabled && !showQrCode && (
                          <div className="mt-4 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-green-400 text-sm font-medium">
                                Two-Factor Authentication is active
                              </span>
                            </div>
                            <p className="text-gray-400 text-xs mt-1">
                              Your account is protected with 2FA. You'll need your authenticator app to sign in.
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Reset HWID PC */}
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Reset HWID PC</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-orange-600 text-orange-400 hover:bg-orange-600/10"
                            onClick={() => setShowHwidResetDialog(true)}
                            disabled={hwidResetLoading}
                          >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Reset HWID
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Reset your hardware ID (deducts 15 days from expiration)</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Key className="w-5 h-5 text-purple-400" />
                      Active Sessions
                    </h3>
                    {sessionsLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-purple-400" />
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {userSessions.map((session) => (
                          <div key={session.id} className="p-4 bg-gray-800/50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                                  {session.device.includes('Mobile') || session.device.includes('iPhone') ? (
                                    <Smartphone className="w-5 h-5 text-blue-400" />
                                  ) : (
                                    <Monitor className="w-5 h-5 text-blue-400" />
                                  )}
                                </div>
                                <div>
                                  <p className="text-white font-medium">{session.isCurrent ? 'Current Session' : session.device}</p>
                                  <p className="text-gray-400 text-sm">{session.device} • {session.browser}</p>
                                </div>
                              </div>
                              {session.isCurrent ? (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-red-600 text-red-400 hover:bg-red-600/10"
                                  onClick={() => revokeSession(session.id)}
                                >
                                  <Trash2 className="w-4 h-4 mr-1" />
                                  Revoke
                                </Button>
                              )}
                            </div>
                            <p className="text-gray-400 text-xs">Last active: {session.lastActive}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'activity' && (
                <motion.div
                  key="activity"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg"
                >
                  <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                    <Activity className="w-5 h-5 text-purple-400" />
                    Recent Activity
                  </h3>

                  {/* Search and Filter Controls */}
                  <div className="mb-6 space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4">
                      {/* Search Input */}
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input
                          type="text"
                          placeholder="Search by operation type, model, or IMEI..."
                          value={activitySearchTerm}
                          onChange={(e) => setActivitySearchTerm(e.target.value)}
                          className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        />
                        {activitySearchTerm && (
                          <button
                            onClick={() => setActivitySearchTerm('')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      {/* Operation Type Filter */}
                      <div className="relative">
                        <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <select
                          value={activityTypeFilter}
                          onChange={(e) => setActivityTypeFilter(e.target.value)}
                          className="pl-10 pr-8 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 min-w-[150px]"
                        >
                          <option value="">All Types</option>
                          {getUniqueOperationTypes(allOperations).map(type => (
                            <option key={type} value={type}>{type}</option>
                          ))}
                        </select>
                      </div>

                      {/* Status Filter */}
                      <div className="relative">
                        <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <select
                          value={activityStatusFilter}
                          onChange={(e) => setActivityStatusFilter(e.target.value)}
                          className="pl-10 pr-8 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 min-w-[120px]"
                        >
                          <option value="">All Status</option>
                          {getUniqueStatuses(allOperations).map(status => (
                            <option key={status} value={status}>{status}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Active Filters Display */}
                    {(activitySearchTerm || activityTypeFilter || activityStatusFilter) && (
                      <div className="flex flex-wrap gap-2">
                        {activitySearchTerm && (
                          <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                            Search: "{activitySearchTerm}"
                            <button onClick={() => setActivitySearchTerm('')}>
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        )}
                        {activityTypeFilter && (
                          <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                            Type: {activityTypeFilter}
                            <button onClick={() => setActivityTypeFilter('')}>
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        )}
                        {activityStatusFilter && (
                          <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                            Status: {activityStatusFilter}
                            <button onClick={() => setActivityStatusFilter('')}>
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    {getPaginatedOperations().length > 0 ? (
                      getPaginatedOperations().map((operation: OperationDetail, index: number) => {
                        const getActivityIcon = (operationType: string) => {
                          switch (operationType.toLowerCase()) {
                            case 'direct unlock':
                              return { icon: Key, color: 'text-green-400' };
                            case 'remove frp':
                            case 'remove frp [brom]':
                            case 'remove frp [dm]':
                              return { icon: Shield, color: 'text-blue-400' };
                            case 'write cert':
                              return { icon: Award, color: 'text-purple-400' };
                            case 'convert csc':
                              return { icon: Settings, color: 'text-yellow-400' };
                            default:
                              return { icon: Activity, color: 'text-gray-400' };
                          }
                        };

                        const { icon: Icon, color } = getActivityIcon(operation.operation_type);

                        return (
                          <motion.div
                            key={operation.operation_id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800/70 transition-colors"
                          >
                            <div className={`w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0`}>
                              <Icon className={`w-5 h-5 ${color}`} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-white font-medium truncate">{operation.operation_type}</p>
                              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-sm text-gray-400">
                                <span className="truncate">{operation.time}</span>
                                {operation.model && (
                                  <>
                                    <span className="hidden sm:inline">•</span>
                                    <span className="truncate">{operation.model}</span>
                                  </>
                                )}
                                {operation.status && (
                                  <>
                                    <span className="hidden sm:inline">•</span>
                                    <span className={`truncate ${
                                      operation.status.toLowerCase() === 'success' ? 'text-green-400' :
                                      operation.status.toLowerCase() === 'failed' ? 'text-red-400' : 'text-yellow-400'
                                    }`}>
                                      {operation.status}
                                    </span>
                                  </>
                                )}
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-purple-600 text-purple-400 hover:bg-purple-600/10 w-full sm:w-auto flex-shrink-0"
                              onClick={() => {
                                setSelectedOperation(operation);
                                setShowOperationDetails(true);
                              }}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View Details
                            </Button>
                          </motion.div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                        <p className="text-gray-400">No recent activity</p>
                        <p className="text-gray-500 text-sm">Your operations will appear here</p>
                      </div>
                    )}
                  </div>

                  {/* Pagination Controls */}
                  {getTotalPages() > 1 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 pt-6 border-t border-gray-700/50">
                      <div className="text-sm text-gray-400 text-center sm:text-left">
                        Showing {((currentPage - 1) * 5) + 1} to {Math.min(currentPage * 5, getFilteredOperations().length)} of {getFilteredOperations().length} operations
                        {(activitySearchTerm || activityTypeFilter || activityStatusFilter) && (
                          <span className="text-purple-400"> (filtered from {totalOperations} total)</span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft className="w-4 h-4" />
                          <span className="hidden sm:inline">Previous</span>
                        </Button>
                        <span className="text-sm text-gray-400 px-2 sm:px-3">
                          {currentPage}/{getTotalPages()}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                          disabled={currentPage === getTotalPages()}
                        >
                          <span className="hidden sm:inline">Next</span>
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* HWID Reset Warning Dialog */}
        {showHwidResetDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 max-w-md w-full"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-orange-500/20 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-5 h-5 text-orange-400" />
                </div>
                <h3 className="text-xl font-semibold text-white">Reset HWID Warning</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-300 mb-3">
                  Are you sure you want to reset your Hardware ID (HWID)?
                </p>
                <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-3">
                  <p className="text-orange-400 text-sm font-medium">
                    ⚠️ This action will deduct 15 days from your account expiration date.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => setShowHwidResetDialog(false)}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                  disabled={hwidResetLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={resetHwid}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
                  disabled={hwidResetLoading}
                >
                  {hwidResetLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Resetting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Reset HWID
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          </div>
        )}

        {/* Operation Details Dialog */}
        {showOperationDetails && selectedOperation && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Operation Details</h3>
                <Button
                  onClick={() => setShowOperationDetails(false)}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Operation Type</p>
                    <p className="text-white font-medium">{selectedOperation.operation_type}</p>
                  </div>

                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Status</p>
                    <p className={`font-medium ${
                      selectedOperation.status?.toLowerCase() === 'success' ? 'text-green-400' :
                      selectedOperation.status?.toLowerCase() === 'failed' ? 'text-red-400' : 'text-yellow-400'
                    }`}>
                      {selectedOperation.status || 'Unknown'}
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-gray-400 text-sm">Time</p>
                    <p className="text-white font-medium">{selectedOperation.time}</p>
                  </div>

                  {selectedOperation.model && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Model</p>
                      <p className="text-white font-medium">{selectedOperation.model}</p>
                    </div>
                  )}

                  {selectedOperation.brand && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Brand</p>
                      <p className="text-white font-medium">{selectedOperation.brand}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  {selectedOperation.imei && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">IMEI</p>
                      <p className="text-white font-medium">{selectedOperation.imei}</p>
                    </div>
                  )}

                  {selectedOperation.android && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Android Version</p>
                      <p className="text-white font-medium">{selectedOperation.android}</p>
                    </div>
                  )}

                  {selectedOperation.baseband && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Baseband</p>
                      <p className="text-white font-medium">{selectedOperation.baseband}</p>
                    </div>
                  )}

                  {selectedOperation.carrier && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Carrier</p>
                      <p className="text-white font-medium">{selectedOperation.carrier}</p>
                    </div>
                  )}

                  {selectedOperation.credit && (
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <p className="text-gray-400 text-sm">Credits Used</p>
                      <p className="text-white font-medium">{selectedOperation.credit}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-700/50">
                <Button
                  onClick={() => setShowOperationDetails(false)}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Close
                </Button>
              </div>
            </motion.div>
          </div>
        )}


      </div>
    </ProtectedRoute>
  );
};

export default NewUserProfilePage;
