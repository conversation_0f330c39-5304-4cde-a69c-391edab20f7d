import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Eye, EyeOff, Shield, Check, X } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface PasswordChangeFormProps {
  userEmail: string;
  onSuccess: () => void;
  onCancel: () => void;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface PasswordErrors {
  [key: string]: string;
}

export const PasswordChangeForm = ({ userEmail, onSuccess, onCancel }: PasswordChangeFormProps) => {
  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  const [passwordErrors, setPasswordErrors] = useState<PasswordErrors>({});
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  // Password strength validation
  const getPasswordStrength = (password: string) => {
    const requirements = [
      { test: password.length >= 8, label: "At least 8 characters" },
      { test: /[A-Z]/.test(password), label: "One uppercase letter" },
      { test: /[a-z]/.test(password), label: "One lowercase letter" },
      { test: /\d/.test(password), label: "One number" },
      { test: /[!@#$%^&*(),.?":{}|<>]/.test(password), label: "One special character" }
    ];

    const passed = requirements.filter(req => req.test).length;
    const strength = passed / requirements.length;

    return {
      requirements,
      strength,
      isStrong: passed >= 4
    };
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: PasswordErrors = {};

    if (!passwordData.currentPassword) {
      errors.currentPassword = "Current password is required";
    }

    if (!passwordData.newPassword) {
      errors.newPassword = "New password is required";
    } else {
      const { isStrong } = getPasswordStrength(passwordData.newPassword);
      if (!isStrong) {
        errors.newPassword = "Password does not meet security requirements";
      }
    }

    if (!passwordData.confirmPassword) {
      errors.confirmPassword = "Please confirm your new password";
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    if (passwordData.currentPassword && passwordData.newPassword && 
        passwordData.currentPassword === passwordData.newPassword) {
      errors.newPassword = "New password must be different from current password";
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      // Verify current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: passwordData.currentPassword
      });

      if (signInError) {
        setPasswordErrors({ currentPassword: "Current password is incorrect" });
        return;
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) {
        toast.error("Failed to update password: " + updateError.message);
        return;
      }

      toast.success("Password updated successfully!");
      onSuccess();

    } catch (error) {
      console.error('Error changing password:', error);
      toast.error("Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof PasswordData, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error
    if (passwordErrors[field]) {
      setPasswordErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const { requirements, strength } = getPasswordStrength(passwordData.newPassword);

  return (
    <div className="mt-4 p-4 bg-gray-900/50 border border-gray-600 rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <Shield className="w-5 h-5 text-purple-400" />
        <h4 className="text-white font-medium">Change Password</h4>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Current Password */}
        <div>
          <Label htmlFor="current_password" className="text-gray-300 text-sm">
            Current Password
          </Label>
          <div className="relative mt-1">
            <Input
              id="current_password"
              type={showPasswords.current ? "text" : "password"}
              value={passwordData.currentPassword}
              onChange={(e) => handleInputChange('currentPassword', e.target.value)}
              className={`bg-gray-800 border-gray-600 text-white focus:border-purple-400 pr-10 ${
                passwordErrors.currentPassword ? 'border-red-500' : ''
              }`}
              placeholder="Enter your current password"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('current')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {passwordErrors.currentPassword && (
            <p className="text-red-400 text-xs mt-1">{passwordErrors.currentPassword}</p>
          )}
        </div>

        {/* New Password */}
        <div>
          <Label htmlFor="new_password" className="text-gray-300 text-sm">
            New Password
          </Label>
          <div className="relative mt-1">
            <Input
              id="new_password"
              type={showPasswords.new ? "text" : "password"}
              value={passwordData.newPassword}
              onChange={(e) => handleInputChange('newPassword', e.target.value)}
              className={`bg-gray-800 border-gray-600 text-white focus:border-purple-400 pr-10 ${
                passwordErrors.newPassword ? 'border-red-500' : ''
              }`}
              placeholder="Enter your new password"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('new')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {passwordErrors.newPassword && (
            <p className="text-red-400 text-xs mt-1">{passwordErrors.newPassword}</p>
          )}
          
          {/* Password Strength Indicator */}
          {passwordData.newPassword && (
            <div className="mt-2">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      strength < 0.3 ? 'bg-red-500' :
                      strength < 0.6 ? 'bg-yellow-500' :
                      strength < 0.8 ? 'bg-blue-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${strength * 100}%` }}
                  />
                </div>
                <span className={`text-xs ${
                  strength < 0.3 ? 'text-red-400' :
                  strength < 0.6 ? 'text-yellow-400' :
                  strength < 0.8 ? 'text-blue-400' : 'text-green-400'
                }`}>
                  {strength < 0.3 ? 'Weak' :
                   strength < 0.6 ? 'Fair' :
                   strength < 0.8 ? 'Good' : 'Strong'}
                </span>
              </div>
              
              <div className="grid grid-cols-1 gap-1">
                {requirements.map((req, index) => (
                  <div key={index} className="flex items-center gap-2 text-xs">
                    {req.test ? (
                      <Check className="w-3 h-3 text-green-400" />
                    ) : (
                      <X className="w-3 h-3 text-gray-500" />
                    )}
                    <span className={req.test ? 'text-green-400' : 'text-gray-500'}>
                      {req.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <Label htmlFor="confirm_password" className="text-gray-300 text-sm">
            Confirm New Password
          </Label>
          <div className="relative mt-1">
            <Input
              id="confirm_password"
              type={showPasswords.confirm ? "text" : "password"}
              value={passwordData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              className={`bg-gray-800 border-gray-600 text-white focus:border-purple-400 pr-10 ${
                passwordErrors.confirmPassword ? 'border-red-500' : ''
              }`}
              placeholder="Confirm your new password"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('confirm')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {passwordErrors.confirmPassword && (
            <p className="text-red-400 text-xs mt-1">{passwordErrors.confirmPassword}</p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            type="submit"
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 flex-1"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Password"
            )}
          </Button>
          <Button
            type="button"
            onClick={onCancel}
            variant="outline"
            disabled={loading}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};
