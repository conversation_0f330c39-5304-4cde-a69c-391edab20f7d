import { SimpleSignUpForm } from "./SimpleSignUpForm";
import { Link } from "react-router-dom";
import { Home, Users, Award, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";

export const SignUpPage = () => {
  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 25% 25%, #8B5CF6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #8B5CF6 0%, transparent 50%)',
          backgroundSize: '400px 400px'
        }} />
      </div>

      {/* Back To Home Button */}
      <div className="absolute top-6 left-6 z-50">
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-[#1a1a1a] border border-gray-700 text-gray-300 hover:bg-[#2a2a2a] hover:text-white hover:border-purple-400 transition-all duration-200"
          >
            <Home className="w-4 h-4 mr-2" />
            Back To Home
          </Button>
        </Link>
      </div>

      <div className="flex min-h-screen">
        {/* Main Content - Centered Form */}
        <div className="flex-1 flex items-center justify-center p-6 relative z-10">
          <div className="w-full max-w-md">
            <SimpleSignUpForm />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-6 left-6 right-6 text-center">
        <p className="text-gray-500 text-xs">
        <span>© {new Date().getFullYear()} Pegasus Tools. All rights reserved.</span>
        </p>
      </div>
    </div>
  );
};

export default SignUpPage;
