import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignInForm, FormErrors } from "@/lib/auth";
import { getSafeRedirectPath } from "@/lib/routes";
import { TwoFactorVerification } from "./TwoFactorVerification";
import { useTwoFactor } from "@/hooks/useTwoFactor";
import { supabase } from "@/integrations/supabase/client";

export const SimpleSignInForm = () => {
  const { signIn, signInWithProvider, loading, redirectAfterAuth, clearRedirect } = useAuth();
  const { checkTwoFactorEnabled, verifyTwoFactorCode } = useTwoFactor();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [pendingUserId, setPendingUserId] = useState<string | null>(null);
  const [is2FAInProgress, setIs2FAInProgress] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignInForm(email, password);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Use Supabase directly to avoid automatic auth state updates
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        setErrors({ general: error.message });
        return;
      }

      if (data?.user) {
        console.log('🔐 User authenticated:', data.user.id);

        // Get user profile data to check 2FA status
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('uid, two_factor_enabled, otp_secret')
          .eq('id', data.user.id)
          .single();

        console.log('👤 User profile data:', userData);
        console.log('🔒 2FA enabled:', userData?.two_factor_enabled);

        if (!userError && userData && userData.two_factor_enabled) {
          console.log('🚨 2FA required - signing out and showing verification form');
          // Set 2FA in progress flag
          setIs2FAInProgress(true);
          // Sign out immediately to prevent automatic redirect
          await supabase.auth.signOut();
          setPendingUserId(userData.uid);
          setShowTwoFactor(true);
          return;
        } else {
          console.log('✅ No 2FA required - proceeding with normal login');
          // For non-2FA users, proceed with normal login flow
          // The auth state will be updated automatically by Supabase
          const destination = getSafeRedirectPath(redirectAfterAuth);
          navigate(destination);
          if (redirectAfterAuth) {
            clearRedirect();
          }
          toast.success("Successfully signed in!");
        }
      }
    } catch (err: any) {
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTwoFactorVerify = async (code: string): Promise<boolean> => {
    if (!pendingUserId) {
      console.error('❌ No pending user ID for 2FA verification');
      return false;
    }

    console.log('🔐 Verifying 2FA code for user:', pendingUserId);

    try {
      const isValid = await verifyTwoFactorCode(pendingUserId, code);
      console.log('✅ 2FA verification result:', isValid);

      if (isValid) {
        console.log('🔄 Re-authenticating with original credentials');
        // Re-authenticate with the original credentials
        const { error } = await signIn(email, password);

        if (!error) {
          console.log('✅ 2FA verification complete - redirecting');
          // Clear 2FA state and redirect
          setShowTwoFactor(false);
          setPendingUserId(null);
          setIs2FAInProgress(false);

          // Navigate to safe redirect path
          const destination = getSafeRedirectPath(redirectAfterAuth);
          navigate(destination);
          if (redirectAfterAuth) {
            clearRedirect();
          }
          toast.success("Successfully signed in!");
          return true;
        } else {
          console.error('❌ Authentication failed after 2FA verification:', error);
          toast.error("Authentication failed after 2FA verification");
        }
      } else {
        console.log('❌ Invalid 2FA code');
      }

      return false;
    } catch (error) {
      console.error('💥 2FA verification error:', error);
      return false;
    }
  };

  const handleBackToSignIn = () => {
    setShowTwoFactor(false);
    setPendingUserId(null);
    setIs2FAInProgress(false);
    setPassword(""); // Clear password for security
  };

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
      // Note: For OAuth providers, the redirect will be handled by the AuthRedirectHandler
      // after the OAuth callback completes
    } catch (err: any) {
      toast.error("Failed to sign in with Google");
    }
  };

  // Show 2FA verification if needed
  if (showTwoFactor) {
    return (
      <TwoFactorVerification
        email={email}
        onVerify={handleTwoFactorVerify}
        onBack={handleBackToSignIn}
        loading={loading}
      />
    );
  }

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
      >
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-white mb-2">Welcome Back</h1>
          <p className="text-gray-400 text-sm">Sign in to your Pegasus Tool account</p>
        </div>

        {/* Social Sign In Button */}
        <Button
          onClick={handleGoogleSignIn}
          variant="outline"
          className="w-full bg-[#111111] border border-gray-700 text-white hover:bg-[#2a2a2a] hover:border-purple-400 transition-all duration-200 h-11"
          disabled={loading}
        >
          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Sign in with Google
        </Button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#1a1a1a] px-3 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11"
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm mt-1">{errors.password}</p>
            )}
          </div>



          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white font-medium h-11 rounded-lg transition-colors duration-200"
          >
            {isSubmitting ? "Signing in..." : "Sign In"}
          </Button>
        </form>

        {/* Links */}
        <div className="space-y-3 text-center">
          <Link
            to="/forgot-password"
            className="block text-gray-400 hover:text-purple-400 text-sm hover:underline transition-colors duration-200"
          >
            Forgot your password?
          </Link>

          <div className="text-sm text-gray-400">
            Don't have an account?{" "}
            <Link
              to="/sign-up"
              className="text-purple-400 hover:text-purple-300 font-medium hover:underline transition-colors duration-200"
            >
              Create Account
            </Link>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignInForm;
