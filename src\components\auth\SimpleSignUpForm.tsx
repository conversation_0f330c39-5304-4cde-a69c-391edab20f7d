import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignUpForm, FormErrors } from "@/lib/auth";
import { getSafeRedirectPath } from "@/lib/routes";

export const SimpleSignUpForm = () => {
  const { signUp, signInWithProvider, loading, redirectAfterAuth, clearRedirect } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignUpForm(email, password, confirmPassword);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await signUp(email, password);

      if (!error) {
        // Account created successfully, user should be automatically signed in
        // Navigate to safe redirect path
        const destination = getSafeRedirectPath(redirectAfterAuth);
        navigate(destination);
        if (redirectAfterAuth) {
          clearRedirect();
        }
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
      // Note: For OAuth providers, the redirect will be handled by the AuthRedirectHandler
      // after the OAuth callback completes
    } catch (err: any) {
      toast.error("Failed to sign up with Google");
    }
  };



  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
      >
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-white mb-2">Create Account</h1>
          <p className="text-gray-400 text-sm">Join the Pegasus Tool community</p>
        </div>

        {/* Social Sign Up Button */}
        <Button
          onClick={handleGoogleSignUp}
          variant="outline"
          className="w-full bg-[#111111] border border-gray-700 text-white hover:bg-[#2a2a2a] hover:border-purple-400 transition-all duration-200 h-11"
          disabled={loading}
        >
          <Chrome className="w-4 h-4 mr-2" />
          Sign up with Google
        </Button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#1a1a1a] px-3 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11"
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                placeholder="Create a password (min. 6 characters)"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm mt-1">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-sm text-gray-300 font-medium">
              Confirm Password
            </Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                placeholder="Confirm your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-400 text-sm mt-1">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Terms and Privacy */}
          <div className="flex items-start space-x-3 p-3 rounded-lg bg-[#111111] border border-gray-700">
            <input
              type="checkbox"
              id="terms"
              className="h-4 w-4 mt-0.5 rounded border-2 border-gray-600 bg-[#111111] text-purple-400 focus:ring-1 focus:ring-purple-400/50 focus:border-purple-400 transition-colors duration-200 cursor-pointer"
              required
            />
            <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed cursor-pointer">
              I agree to the{" "}
              <Link
                to="/terms-of-service"
                className="text-purple-400 hover:text-purple-300 font-medium underline transition-colors duration-200"
              >
                Terms of Service
              </Link>
              {" "}and{" "}
              <Link
                to="/privacy-policy"
                className="text-purple-400 hover:text-purple-300 font-medium underline transition-colors duration-200"
              >
                Privacy Policy
              </Link>
            </label>
          </div>



          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password || !confirmPassword}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white font-medium h-11 rounded-lg transition-colors duration-200"
          >
            {isSubmitting ? "Creating account..." : "Create Account"}
          </Button>
        </form>

        {/* Sign In Link */}
        <div className="text-center">
          <div className="text-sm text-gray-400">
            Already have an account?{" "}
            <Link
              to="/sign-in"
              className="text-purple-400 hover:text-purple-300 font-medium hover:underline transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignUpForm;
