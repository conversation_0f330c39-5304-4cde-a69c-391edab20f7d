import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Shield, ArrowLeft, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface TwoFactorVerificationProps {
  email: string;
  onVerify: (code: string) => Promise<boolean>;
  onBack: () => void;
  loading?: boolean;
}

export const TwoFactorVerification = ({ 
  email, 
  onVerify, 
  onBack, 
  loading = false 
}: TwoFactorVerificationProps) => {
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (verificationCode.length !== 6) {
      toast.error("Please enter a valid 6-digit code");
      return;
    }

    setIsVerifying(true);

    try {
      const isValid = await onVerify(verificationCode);
      
      if (!isValid) {
        toast.error("Invalid verification code. Please try again.");
        setVerificationCode("");
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      toast.error("Verification failed. Please try again.");
      setVerificationCode("");
    } finally {
      setIsVerifying(false);
    }
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setVerificationCode(value);
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
      >
        {/* Header */}
        <div className="text-center">
          <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-purple-400" />
          </div>
          <h1 className="text-2xl font-semibold text-white mb-2">Two-Factor Authentication</h1>
          <p className="text-gray-400 text-sm">
            Enter the 6-digit code from your authenticator app
          </p>
          <p className="text-gray-500 text-xs mt-1">
            Signing in as: <span className="text-purple-400">{email}</span>
          </p>
        </div>

        {/* Verification Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="verification_code" className="text-gray-300 text-sm font-medium">
              Verification Code
            </Label>
            <Input
              id="verification_code"
              type="text"
              value={verificationCode}
              onChange={handleCodeChange}
              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400 text-center text-2xl font-mono tracking-widest mt-2"
              placeholder="000000"
              maxLength={6}
              autoComplete="one-time-code"
              autoFocus
            />
            <p className="text-gray-500 text-xs mt-1">
              Enter the code from your authenticator app
            </p>
          </div>

          <Button
            type="submit"
            className="w-full bg-purple-600 hover:bg-purple-700 h-11"
            disabled={isVerifying || loading || verificationCode.length !== 6}
          >
            {isVerifying ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify & Sign In"
            )}
          </Button>
        </form>

        {/* Back Button */}
        <div className="pt-4 border-t border-gray-700">
          <Button
            onClick={onBack}
            variant="ghost"
            className="w-full text-gray-400 hover:text-white hover:bg-gray-800"
            disabled={isVerifying || loading}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Sign In
          </Button>
        </div>

        {/* Help Text */}
        <div className="text-center">
          <p className="text-gray-500 text-xs">
            Don't have access to your authenticator app?{" "}
            <button 
              type="button"
              className="text-purple-400 hover:text-purple-300 underline"
              onClick={() => toast.info("Please contact support for account recovery")}
            >
              Contact Support
            </button>
          </p>
        </div>
      </motion.div>
    </div>
  );
};
