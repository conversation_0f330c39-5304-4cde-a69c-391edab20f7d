import { useAuth } from "@/contexts/AuthContext";
import { useState, useRef, useEffect } from "react";
import { Settings, LogOut, User } from "lucide-react";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { useLocation, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";

export const UserAvatar = () => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<'right' | 'left'>('right');

  // Function to get theme colors based on current page (matching Navbar)
  const getThemeColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      // Home page - Purple theme
      return {
        primary: '#C084FC',
        primaryRgb: '192, 132, 252',
        background: 'from-purple-900/95 to-purple-800/90',
        border: 'border-purple-500/30',
        accent: 'bg-purple-500/20',
        accentHover: 'group-hover:bg-purple-500/30',
        text: 'text-purple-400',
        glow: 'shadow-purple-500/20',
        ring: 'ring-purple-500/20'
      };
    } else if (currentPath === '/software') {
      // Software page - Orange theme
      return {
        primary: '#F97316',
        primaryRgb: '249, 115, 22',
        background: 'from-orange-900/95 to-orange-800/90',
        border: 'border-orange-500/30',
        accent: 'bg-orange-500/20',
        accentHover: 'group-hover:bg-orange-500/30',
        text: 'text-orange-400',
        glow: 'shadow-orange-500/20',
        ring: 'ring-orange-500/20'
      };
    } else if (currentPath === '/hardware') {
      // Hardware page - Blue theme
      return {
        primary: '#3B82F6',
        primaryRgb: '59, 130, 246',
        background: 'from-blue-900/95 to-blue-800/90',
        border: 'border-blue-500/30',
        accent: 'bg-blue-500/20',
        accentHover: 'group-hover:bg-blue-500/30',
        text: 'text-blue-400',
        glow: 'shadow-blue-500/20',
        ring: 'ring-blue-500/20'
      };
    } else {
      // Default - Purple theme
      return {
        primary: '#C084FC',
        primaryRgb: '192, 132, 252',
        background: 'from-purple-900/95 to-purple-800/90',
        border: 'border-purple-500/30',
        accent: 'bg-purple-500/20',
        accentHover: 'group-hover:bg-purple-500/30',
        text: 'text-purple-400',
        glow: 'shadow-purple-500/20',
        ring: 'ring-purple-500/20'
      };
    }
  };

  const themeColors = getThemeColors();

  if (!user) {
    return null;
  }

  const handleSignOut = async () => {
    try {
      await signOut();
      setShowMenu(false);
      // Navigate to home page after successful sign out
      navigate('/');
    } catch (error) {
      // Error handling without console output
    }
  };

  const handleManageAccount = () => {
    // Navigate to profile page using React Router
    setShowMenu(false);
    navigate('/profile');
  };

  // Determine the menu position based on the element's position on the screen
  useEffect(() => {
    if (showMenu && menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect();
      const windowWidth = window.innerWidth;

      // If the menu will be outside the right side, display it from the left side
      if (rect.right > windowWidth - 20) {
        setMenuPosition('left');
      } else {
        setMenuPosition('right');
      }
    }
  }, [showMenu]);

  const userName = getUserDisplayName(user);
  const userEmail = user.email || '';
  const userAvatar = getUserAvatar(user);

  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  return (
    <div className="relative">
      {/* Enhanced User Avatar with Dynamic Theme */}
      <motion.div
        className={`w-10 h-10 rounded-full border-2 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden ring-2 ring-transparent ${themeColors.border} hover:shadow-xl ${themeColors.glow} hover:scale-105 active:scale-95 hover:${themeColors.ring}`}
        onClick={toggleMenu}
        title={`Account Management - ${userName}`}
        whileHover={{
          scale: 1.05,
          boxShadow: `0 0 20px rgba(${themeColors.primaryRgb}, 0.4)`
        }}
        whileTap={{ scale: 0.95 }}
        style={{
          borderColor: `rgba(${themeColors.primaryRgb}, 0.5)`
        }}
      >
        {userAvatar ? (
          <img
            src={userAvatar}
            alt="User Avatar"
            className="w-full h-full object-cover rounded-full transition-all duration-300 hover:brightness-110"
            onError={(e) => {
              // In case of image loading failure, display the first letter
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <div
            className="w-full h-full flex items-center justify-center text-white font-bold text-sm shadow-inner"
            style={{
              background: `linear-gradient(135deg, rgba(${themeColors.primaryRgb}, 0.8) 0%, rgba(${themeColors.primaryRgb}, 0.6) 100%)`
            }}
          >
            {userName.charAt(0).toUpperCase()}
          </div>
        )}
      </motion.div>

      {/* Enhanced Dropdown Menu with Dynamic Theme */}
      <AnimatePresence>
        {showMenu && (
          <>
            {/* Transparent background for closing the menu */}
            <motion.div
              className="fixed inset-0 z-40"
              onClick={() => setShowMenu(false)}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />

            {/* Simplified Menu with Solid Colors */}
            <motion.div
              ref={menuRef}
              className={`absolute top-full mt-3 w-64 border rounded-lg z-50 overflow-hidden ${
                menuPosition === 'right' ? 'right-0' : 'left-0'
              }`}
              style={{
                backgroundColor: '#1f2937',
                borderColor: themeColors.primary,
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)'
              }}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              {/* Simplified User Information */}
              <div
                className="p-4 border-b"
                style={{
                  borderColor: themeColors.primary,
                  backgroundColor: '#374151'
                }}
              >
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-full overflow-hidden border-2"
                    style={{
                      borderColor: themeColors.primary
                    }}
                  >
                    {userAvatar ? (
                      <img
                        src={userAvatar}
                        alt="User Avatar"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div
                        className="w-full h-full flex items-center justify-center text-white font-bold text-base"
                        style={{
                          backgroundColor: themeColors.primary
                        }}
                      >
                        {userName.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-white font-semibold text-base truncate">
                      {userName}
                    </div>
                    <div className="text-gray-300 text-sm truncate">
                      {userEmail}
                    </div>
                    <div
                      className="text-xs font-medium mt-1 px-2 py-0.5 rounded inline-block"
                      style={{
                        color: themeColors.primary,
                        backgroundColor: '#4b5563'
                      }}
                    >
                      ● Active
                    </div>
                  </div>
                </div>
              </div>

              {/* Simplified Menu Options */}
              <div className="p-2">
                <button
                  onClick={handleManageAccount}
                  className="w-full text-left text-gray-200 hover:text-white transition-all duration-200 rounded-lg p-3 flex items-center gap-3 hover:bg-gray-600"
                >
                  <div
                    className="w-8 h-8 rounded-lg flex items-center justify-center"
                    style={{
                      backgroundColor: themeColors.primary
                    }}
                  >
                    <Settings
                      className="w-4 h-4 text-white"
                    />
                  </div>
                  <div className="flex-1">
                    <span className="text-white font-medium text-sm block">Manage Account</span>
                    <span className="text-gray-400 text-xs">Profile settings</span>
                  </div>
                </button>

                <button
                  onClick={handleSignOut}
                  className="w-full text-left text-gray-200 hover:text-red-300 transition-all duration-200 rounded-lg p-3 flex items-center gap-3 hover:bg-red-900/20"
                >
                  <div
                    className="w-8 h-8 rounded-lg bg-red-500 flex items-center justify-center"
                  >
                    <LogOut className="text-white w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-sm block text-white">Sign Out</span>
                    <span className="text-gray-400 text-xs">End session</span>
                  </div>
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserAvatar;
