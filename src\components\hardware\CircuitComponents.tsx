
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { use3DEffect } from '@/hooks/use3DEffect';
import Text3D from '@/components/3D/Text3D';
import { HardDrive, MemoryStick, Cpu } from 'lucide-react';

interface ComponentData {
  name: string;
  description: string;
  image: string;
  function: string;
}

const CircuitComponents = () => {
  const componentRef = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: true
  });

  // Component data
  const components: ComponentData[] = [
    {
      name: 'Main Processor',
      description: 'The central processing unit that controls all device operations.',
      image: '/images/processor.png',
      function: 'Executes program instructions and manages device operations.'
    },
    {
      name: 'Flash Memory Chip',
      description: 'Non-volatile storage chip that retains data even when powered off.',
      image: '/images/memory-chip.png',
      function: 'Stores firmware, operating system, and critical device data.'
    },
    {
      name: 'USB Controller',
      description: 'Manages USB communications between the device and connected phones.',
      image: '/images/usb-controller.png',
      function: 'Handles data transfer protocols and connection management.'
    }
  ];

  const [activeComponent, setActiveComponent] = useState(0);

  const currentComponent = components[activeComponent];

  return (
    <section id="circuit-components" className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="mb-6">
            <span className="bg-[#1a1a1a] border border-gray-700 text-pegasus-blue-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
              Circuit Components
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            Key Hardware <span className="text-pegasus-blue-400">Components</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Explore the key components used in our hardware design and diagnostics
          </p>
        </motion.div>

        <div className="flex flex-col lg:flex-row items-center gap-10 mt-16">
          {/* Left side - Interactive Component Viewer */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="relative">
              <Card
                ref={componentRef}
                className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2 shadow-xl"
              >
                <div className="flex justify-center mb-6">
                  <div className="relative w-64 h-64 bg-gray-800/50 rounded-lg p-4 flex items-center justify-center">
                    <img
                      src={currentComponent.image}
                      alt={currentComponent.name}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                </div>

                <div className="text-center mb-6">
                  <Text3D
                    as="h3"
                    size="xl"
                    color="text-pegasus-blue-400"
                    className="mb-2"
                  >
                    {currentComponent.name}
                  </Text3D>
                </div>

                <div className="flex justify-center mt-4 gap-2">
                  {components.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveComponent(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === activeComponent ? 'bg-pegasus-blue-500' : 'bg-gray-600'
                      }`}
                      aria-label={`View component ${index + 1}`}
                    />
                  ))}
                </div>
              </Card>
            </div>
          </motion.div>

          {/* Right side - Component Description */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="space-y-6">
              <Text3D
                as="h2"
                size="3xl"
                text-pegasus-blue-600
                className="mb-6"
              >
                Key Hardware Components
              </Text3D>

              <p className="text-lg text-gray-700 dark:text-gray-300">
                {currentComponent.description}
              </p>

              <div className="bg-[#1a1a1a] border border-gray-700/50 p-6 rounded-xl backdrop-blur-lg">
                <h4 className="font-bold text-lg mb-2 text-pegasus-blue-400">
                  Function:
                </h4>
                <p className="text-gray-300">
                  {currentComponent.function}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
                    <HardDrive className="h-6 w-6 text-pegasus-blue-400" />
                  </div>
                  <h5 className="font-medium text-white">Storage</h5>
                </div>

                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
                    <MemoryStick className="h-6 w-6 text-pegasus-blue-400" />
                  </div>
                  <h5 className="font-medium text-white">Memory</h5>
                </div>

                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
                    <Cpu className="h-6 w-6 text-pegasus-blue-400" />
                  </div>
                  <h5 className="font-medium text-white">Processing</h5>
                </div>
              </div>

              <Button
                className="mt-6 bg-pegasus-blue-500 hover:bg-pegasus-blue-600 text-white px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1"
                onClick={() => document.getElementById('hardware-supported-models')?.scrollIntoView({ behavior: 'smooth' })}
              >
                View Supported Models
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-pegasus-blue-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CircuitComponents;
