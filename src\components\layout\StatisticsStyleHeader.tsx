import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { usePageColors } from '@/hooks/usePageColors';

interface StatisticsStyleHeaderProps {
  badge: string;
  title: string;
  highlightWord?: string;
  subtitle: string;
  className?: string;
}

const StatisticsStyleHeader: React.FC<StatisticsStyleHeaderProps> = ({
  badge,
  title,
  highlightWord,
  subtitle,
  className
}) => {
  const colors = usePageColors();

  const processTitle = () => {
    if (!highlightWord) return title;

    const parts = title.split(highlightWord);
    return (
      <>
        {parts[0]}
        <span className={colors.text}>{highlightWord}</span>
        {parts[1]}
      </>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={cn("text-center mb-12", className)}
    >
      <div className="mb-4">
        <span className={`bg-[#1a1a1a] border border-gray-700 ${colors.text} px-3 py-1.5 rounded-full text-sm font-medium`}>
          {badge}
        </span>
      </div>
      <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white leading-tight mb-3">
        {processTitle()}
      </h2>
      <p className="text-sm sm:text-base lg:text-lg text-gray-400 max-w-xl mx-auto">
        {subtitle}
      </p>
    </motion.div>
  );
};

export default StatisticsStyleHeader;
