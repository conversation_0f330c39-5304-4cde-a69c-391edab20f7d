
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { usePageColors } from "@/hooks/usePageColors";

export const ScrollTop = () => {
  const [showButton, setShowButton] = useState(false);
  const colors = usePageColors();

  useEffect(() => {
    const handleScroll = () => {
      // Show the button when scrolled down 300px
      if (window.scrollY > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  return (
    <AnimatePresence>
      {showButton && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3 }}
        >
          <Button
            variant="outline"
            size="icon"
            className="fixed bottom-6 left-6 z-50 p-4 text-white border-none rounded-full shadow-2xl hover:scale-110 transition-all duration-300"
            style={{
              backgroundColor: colors.accent,
              boxShadow: `0 25px 50px -12px ${colors.accent}40`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.accent;
              e.currentTarget.style.filter = 'brightness(1.1)';
              e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.accent}60`;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = colors.accent;
              e.currentTarget.style.filter = 'brightness(1)';
              e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.accent}40`;
            }}
            onClick={scrollToTop}
            aria-label="Scroll to top"
          >
            <ArrowUp className="h-6 w-6" />
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
