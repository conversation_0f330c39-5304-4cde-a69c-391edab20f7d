import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, options?: { data?: any }) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signInWithProvider: (provider: 'google') => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: any) => Promise<{ error: AuthError | null }>;
  // Redirect functionality
  redirectAfterAuth: string | null;
  setRedirectAfterAuth: (path: string | null) => void;
  clearRedirect: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [redirectAfterAuth, setRedirectAfterAuth] = useState<string | null>(null);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
      } else {
        setSession(session);
        setUser(session?.user ?? null);
      }
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, options?: { data?: any }) => {
    try {
      setLoading(true);

      const result = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: options?.data || {}
        }
      });

      if (result.error) {
        console.error('Supabase auth error:', result.error);
        toast.error(result.error.message);
        return { error: result.error };
      }

      // Since autoconfirm is enabled, user should be automatically signed in
      if (result.data?.user && result.data?.session) {
        toast.success('Account created successfully! Welcome!');
      } else {
        toast.success('Account created successfully!');
      }

      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      console.error('Sign up error:', authError);
      toast.error(authError.message || 'Sign up failed');
      return { error: authError };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      const result = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (result.error) {
        console.error('Supabase auth error:', result.error);
        toast.error(result.error.message);
        return { error: result.error };
      }

      toast.success('Successfully signed in!');
      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      console.error('Sign in error:', authError);
      toast.error(authError.message || 'Sign in failed');
      return { error: authError };
    } finally {
      setLoading(false);
    }
  };

  const signInWithProvider = async (provider: 'google') => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/`
        }
      });

      if (error) {
        toast.error(error.message);
        return { error };
      }

      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message);
      return { error: authError };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast.error(error.message);
        throw error;
      }
      // Clear local state immediately
      setUser(null);
      setSession(null);
    } catch (error) {
      toast.error('Error signing out');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setLoading(true);

      // Use the auth callback page which will handle the redirect properly
      const redirectUrl = `${window.location.protocol}//${window.location.host}/auth/callback`;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });

      if (error) {
        toast.error(error.message);
        return { error };
      }

      toast.success('Password reset email sent! Check your inbox and spam folder.');
      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message || 'Failed to send reset email');
      return { error: authError };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: any) => {
    try {
      const { error } = await supabase.auth.updateUser({
        data: updates
      });

      if (error) {
        toast.error(error.message);
        return { error };
      }

      toast.success('Profile updated successfully!');
      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message);
      return { error: authError };
    }
  };

  // Redirect management functions
  const clearRedirect = () => {
    console.log('Clearing redirect path');
    setRedirectAfterAuth(null);
    // Also clear from localStorage
    localStorage.removeItem('redirectAfterAuth');
  };

  // Safe redirect path setter with validation
  const safeSetRedirectAfterAuth = (path: string | null) => {
    if (!path) {
      console.log('Setting redirect path to null');
      setRedirectAfterAuth(null);
      return;
    }

    // Validate and sanitize the path
    const trimmedPath = path.trim();

    // Check for suspicious single-character paths
    if (trimmedPath.length <= 1 && trimmedPath !== '/') {
      console.warn('Rejecting suspicious redirect path:', trimmedPath);
      return;
    }

    // Check for email-like strings that shouldn't be paths
    if (trimmedPath.includes('@') && !trimmedPath.startsWith('/')) {
      console.warn('Rejecting email-like redirect path:', trimmedPath);
      return;
    }

    console.log('Setting redirect path to:', trimmedPath);
    setRedirectAfterAuth(trimmedPath);
  };

  // Load redirect path from localStorage on mount
  useEffect(() => {
    try {
      const savedRedirect = localStorage.getItem('redirectAfterAuth');
      console.log('Loading redirect from localStorage:', savedRedirect);

      if (savedRedirect) {
        // Validate the saved redirect before using it
        if (savedRedirect.trim().length > 1 || savedRedirect === '/') {
          safeSetRedirectAfterAuth(savedRedirect);
        } else {
          console.warn('Invalid saved redirect path, clearing:', savedRedirect);
          localStorage.removeItem('redirectAfterAuth');
        }
      }
    } catch (error) {
      console.error('Error loading redirect from localStorage:', error);
      localStorage.removeItem('redirectAfterAuth');
    }
  }, []);

  // Save redirect path to localStorage when it changes
  useEffect(() => {
    try {
      if (redirectAfterAuth) {
        console.log('Saving redirect to localStorage:', redirectAfterAuth);
        localStorage.setItem('redirectAfterAuth', redirectAfterAuth);
      } else {
        console.log('Removing redirect from localStorage');
        localStorage.removeItem('redirectAfterAuth');
      }
    } catch (error) {
      console.error('Error saving redirect to localStorage:', error);
    }
  }, [redirectAfterAuth]);

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithProvider,
    signOut,
    resetPassword,
    updateProfile,
    redirectAfterAuth,
    setRedirectAfterAuth: safeSetRedirectAfterAuth,
    clearRedirect,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
