import { useLocation } from 'react-router-dom';

export interface PageColors {
  accent: string;
  text: string;
  hover: string;
  active: string;
  border: string;
  background: string;
  gradient: string;
}

export const usePageColors = (): PageColors => {
  const location = useLocation();
  const currentPath = location.pathname;

  if (currentPath === '/software') {
    // Software page - Orange theme
    return {
      accent: '#F97316',
      text: 'text-orange-400',
      hover: 'hover:bg-orange-500/10',
      active: 'bg-orange-500/10',
      border: 'border-orange-500/20',
      background: 'bg-orange-500/5',
      gradient: 'from-orange-500/10 to-orange-600/5'
    };
  } else if (currentPath === '/hardware') {
    // Hardware page - Blue theme
    return {
      accent: '#3B82F6',
      text: 'text-blue-400',
      hover: 'hover:bg-blue-500/10',
      active: 'bg-blue-500/10',
      border: 'border-blue-500/20',
      background: 'bg-blue-500/5',
      gradient: 'from-blue-500/10 to-blue-600/5'
    };
  } else {
    // Default - Purple theme
    return {
      accent: '#C084FC',
      text: 'text-purple-400',
      hover: 'hover:bg-purple-500/10',
      active: 'bg-purple-500/10',
      border: 'border-purple-500/20',
      background: 'bg-purple-500/5',
      gradient: 'from-purple-500/10 to-purple-600/5'
    };
  }
};
