import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { 
  getTrialInfo, 
  getUserLimits, 
  shouldHandleTrialExpiration,
  getTrialExpirationData,
  type TrialInfo 
} from '@/utils/trialUtils';
import { type UserProfileData } from './useUserProfile';

export interface TrialManagementHook {
  checkTrialStatus: (userData: UserProfileData) => TrialInfo;
  handleTrialExpiration: (userData: UserProfileData) => Promise<boolean>;
  getEffectiveLimits: (userData: UserProfileData, planLimits?: any) => {
    unlock_limit: string;
    readcode_limit: string;
    other_operations_limit: string;
  };
  extendTrial: (userId: string, additionalDays: number) => Promise<boolean>;
  upgradeToPremium: (userId: string, planName: string) => Promise<boolean>;
  isOperationAllowed: (userData: UserProfileData, operationType: 'unlock' | 'readcode' | 'other', currentUsage: number) => boolean;
}

export const useTrialManagement = (): TrialManagementHook => {
  const [loading, setLoading] = useState(false);

  /**
   * Check current trial status for a user
   */
  const checkTrialStatus = useCallback((userData: UserProfileData): TrialInfo => {
    return getTrialInfo(userData);
  }, []);

  /**
   * Handle trial expiration by updating user status
   */
  const handleTrialExpiration = useCallback(async (userData: UserProfileData): Promise<boolean> => {
    if (!shouldHandleTrialExpiration(userData)) {
      return false;
    }

    setLoading(true);
    try {
      const expirationData = getTrialExpirationData();
      
      const { error } = await supabase
        .from('users')
        .update(expirationData)
        .eq('uid', userData.uid);

      if (error) throw error;

      toast.info('Your free trial has expired', {
        description: 'Please choose a plan to continue using our services.',
        action: {
          label: 'View Plans',
          onClick: () => {
            // Navigate to pricing page
            const pricingSection = document.getElementById('pricing');
            if (pricingSection) {
              pricingSection.scrollIntoView({ behavior: 'smooth' });
            }
          }
        }
      });

      return true;
    } catch (error) {
      console.error('Error handling trial expiration:', error);
      toast.error('Failed to update trial status');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get effective limits based on trial status and plan
   */
  const getEffectiveLimits = useCallback((userData: UserProfileData, planLimits?: any) => {
    const trialInfo = getTrialInfo(userData);
    return getUserLimits(trialInfo, planLimits);
  }, []);

  /**
   * Extend trial period (admin function)
   */
  const extendTrial = useCallback(async (userId: string, additionalDays: number): Promise<boolean> => {
    setLoading(true);
    try {
      // Get current user data
      const { data: userData, error: fetchError } = await supabase
        .from('users')
        .select('trial_end_date, subscription_status')
        .eq('uid', userId)
        .single();

      if (fetchError || !userData) {
        throw new Error('User not found');
      }

      // Calculate new end date
      const currentEndDate = userData.trial_end_date ? new Date(userData.trial_end_date) : new Date();
      const newEndDate = new Date(currentEndDate);
      newEndDate.setDate(newEndDate.getDate() + additionalDays);

      const { error } = await supabase
        .from('users')
        .update({
          trial_end_date: newEndDate.toISOString().split('T')[0],
          subscription_status: 'trial',
          expiry_time: newEndDate.toISOString().split('T')[0]
        })
        .eq('uid', userId);

      if (error) throw error;

      toast.success(`Trial extended by ${additionalDays} days`);
      return true;
    } catch (error) {
      console.error('Error extending trial:', error);
      toast.error('Failed to extend trial');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Upgrade user to premium plan
   */
  const upgradeToPremium = useCallback(async (userId: string, planName: string): Promise<boolean> => {
    setLoading(true);
    try {
      // This would typically integrate with a payment system
      // For now, we'll just update the user status
      const { error } = await supabase
        .from('users')
        .update({
          subscription_status: 'active',
          user_type: 'premium',
          plan_name: planName,
          expiry_time: null, // Premium doesn't expire
          // Note: Limits would be set based on the specific plan
        })
        .eq('uid', userId);

      if (error) throw error;

      toast.success('Successfully upgraded to premium!', {
        description: 'You now have full access to all features.'
      });
      return true;
    } catch (error) {
      console.error('Error upgrading to premium:', error);
      toast.error('Failed to upgrade account');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Check if a specific operation is allowed based on current limits and usage
   */
  const isOperationAllowed = useCallback((
    userData: UserProfileData, 
    operationType: 'unlock' | 'readcode' | 'other', 
    currentUsage: number
  ): boolean => {
    const trialInfo = getTrialInfo(userData);
    
    // If trial is active, allow unlimited operations
    if (trialInfo.isTrialActive) {
      return true;
    }

    // If premium user, check plan limits
    if (trialInfo.subscriptionStatus === 'active') {
      // This would check against plan-specific limits
      return true; // Simplified for now
    }

    // For expired users, no operations allowed
    if (trialInfo.subscriptionStatus === 'expired') {
      return false;
    }

    // Check against current limits
    const limits = getEffectiveLimits(userData);
    let limit: string;
    
    switch (operationType) {
      case 'unlock':
        limit = limits.unlock_limit;
        break;
      case 'readcode':
        limit = limits.readcode_limit;
        break;
      case 'other':
        limit = limits.other_operations_limit;
        break;
      default:
        return false;
    }

    const limitNumber = parseInt(limit) || 0;
    return currentUsage < limitNumber;
  }, [getEffectiveLimits]);

  return {
    checkTrialStatus,
    handleTrialExpiration,
    getEffectiveLimits,
    extendTrial,
    upgradeToPremium,
    isOperationAllowed
  };
};

export default useTrialManagement;
