import { supabase } from "@/integrations/supabase/client";

// Base32 decoding for TOTP secret (RFC 4648)
const base32Decode = (encoded: string): Uint8Array => {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
  // Remove padding and convert to uppercase
  const cleanInput = encoded.toUpperCase().replace(/=+$/, '').replace(/[^A-Z2-7]/g, '');

  let bits = 0;
  let value = 0;
  const output = [];

  for (let i = 0; i < cleanInput.length; i++) {
    const index = alphabet.indexOf(cleanInput[i]);
    if (index === -1) continue;

    value = (value << 5) | index;
    bits += 5;

    if (bits >= 8) {
      output.push((value >>> (bits - 8)) & 255);
      bits -= 8;
    }
  }

  return new Uint8Array(output);
};

// HMAC-SHA1 implementation
const hmacSha1 = async (key: Uint8Array, message: Uint8Array): Promise<Uint8Array> => {
  const keyBuffer = new ArrayBuffer(key.length);
  const keyView = new Uint8Array(keyBuffer);
  keyView.set(key);

  const messageBuffer = new ArrayBuffer(message.length);
  const messageView = new Uint8Array(messageBuffer);
  messageView.set(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageBuffer);
  return new Uint8Array(signature);
};

// Get correct UTC time regardless of system settings
const getCorrectUnixTime = async (): Promise<number> => {
  try {
    // Try to get time from a reliable source
    const response = await fetch('https://worldtimeapi.org/api/timezone/UTC', {
      method: 'GET',
      cache: 'no-cache'
    });

    if (response.ok) {
      const data = await response.json();
      return Math.floor(new Date(data.datetime).getTime() / 1000);
    }
  } catch (error) {
    // Fallback to local time
  }

  return Math.floor(Date.now() / 1000);
};

// TOTP implementation with correct time synchronization
const generateTOTP = async (secret: string, customTimeStep?: number): Promise<string> => {
  // Decode the base32 secret
  const key = base32Decode(secret);

  let timeStep: number;

  if (customTimeStep !== undefined) {
    timeStep = customTimeStep;
  } else {
    // Get correct UTC time
    const correctUnixTime = await getCorrectUnixTime();
    timeStep = Math.floor(correctUnixTime / 30);
  }

  // Convert time step to 8-byte array (big-endian)
  const timeBytes = new ArrayBuffer(8);
  const timeView = new DataView(timeBytes);
  timeView.setUint32(0, 0, false); // High 32 bits = 0
  timeView.setUint32(4, timeStep, false); // Low 32 bits = time step

  // Create proper ArrayBuffer for the key
  const keyBuffer = new ArrayBuffer(key.length);
  const keyView = new Uint8Array(keyBuffer);
  keyView.set(key);

  // Import key for HMAC-SHA1
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  );

  // Generate HMAC-SHA1
  const signature = await crypto.subtle.sign('HMAC', cryptoKey, timeBytes);
  const hmac = new Uint8Array(signature);

  // Dynamic truncation
  const offset = hmac[19] & 0x0f;
  const code = (
    ((hmac[offset] & 0x7f) << 24) |
    (hmac[offset + 1] << 16) |
    (hmac[offset + 2] << 8) |
    hmac[offset + 3]
  ) % 1000000;

  return code.toString().padStart(6, '0');
};

// Verify TOTP code
const verifyTOTP = async (secret: string, token: string): Promise<boolean> => {
  // Get correct time step using the same method as generateTOTP
  const correctUnixTime = await getCorrectUnixTime();
  const currentTimeStep = Math.floor(correctUnixTime / 30);

  // Check current time window and ±1 window for clock drift
  for (let i = -1; i <= 1; i++) {
    const expectedToken = await generateTOTP(secret, currentTimeStep + i);
    if (expectedToken === token) {
      return true;
    }
  }

  return false;
};

export const useTwoFactor = () => {
  // Check if user has 2FA enabled
  const checkTwoFactorEnabled = async (userId: string): Promise<{ enabled: boolean; secret?: string }> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('two_factor_enabled, otp_secret')
        .eq('uid', userId)
        .single();

      if (error) {
        return { enabled: false };
      }

      return {
        enabled: data?.two_factor_enabled || false,
        secret: data?.otp_secret || undefined
      };
    } catch (error) {
      return { enabled: false };
    }
  };

  // Verify 2FA code for a user
  const verifyTwoFactorCode = async (userId: string, code: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('otp_secret')
        .eq('uid', userId)
        .single();

      if (error || !data?.otp_secret) {
        return false;
      }

      return await verifyTOTP(data.otp_secret, code);
    } catch (error) {
      return false;
    }
  };

  return {
    checkTwoFactorEnabled,
    verifyTwoFactorCode
  };
};
