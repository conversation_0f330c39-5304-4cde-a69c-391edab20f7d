import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import {
  createTrialUserData,
  getTrialInfo,
  shouldHandleTrialExpiration,
  getTrialExpirationData,
  getUserLimits,
  type TrialInfo
} from '@/utils/trialUtils';

export interface UserProfileData {
  id: string;
  uid: string;
  name: string | null;
  email: string;
  phone: string | null;
  country: string | null;
  credits: string | null;
  user_type: string | null;
  activate: string | null;
  block: string | null;
  expiry_time: string | null;
  start_date: string | null;
  hwid: string | null;
  otp_secret: string | null;
  two_factor_enabled: boolean | null;
  my_plans: string | null;
  unlock_limit: number | null;
  readcode_limit: number | null;
  other_operations_limit: string | null;
}

export interface UserStats {
  totalSessions: number;
  totalOperations: number;
  joinDate: string;
  lastLogin: string;
  accountStatus: 'active' | 'blocked' | 'expired';
  securityScore: number;
  profileViews: number;
  userRating: number;
  unlockOperationsUsed: number;
  readcodeOperationsUsed: number;
  otherOperationsUsed: number;
  totalFreeOperations: number;
  totalPaidOperations: number;
}

export interface RecentActivity {
  operation_type: string;
  time: string;
  status: string;
  model?: string;
  brand?: string;
}

// Global flag to prevent multiple profile creation attempts
let isCreatingProfileGlobal = false;

export const useUserProfile = (authUser: User | null) => {
  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [trialInfo, setTrialInfo] = useState<TrialInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile data from the users table
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('uid', userId)
        .maybeSingle(); // Use maybeSingle instead of single to handle 406 errors

      if (error) {
        // Handle 406 errors as if user doesn't exist
        if (error.code === 'PGRST301' || error.code === 'PGRST116' || error.message?.includes('406')) {
          // User not found in users table, create a new record with trial
          if (isCreatingProfileGlobal) {
            return;
          }

          isCreatingProfileGlobal = true;

          try {
            await createUserProfile(userId);
          } finally {
            isCreatingProfileGlobal = false;
          }
          return;
        }
        throw error;
      }

      // If no data returned (null), treat as user not found
      if (!data) {
        if (isCreatingProfileGlobal) {
          return;
        }

        isCreatingProfileGlobal = true;

        try {
          await createUserProfile(userId);
        } finally {
          isCreatingProfileGlobal = false;
        }
        return;
      }

      // Check if trial has expired and handle it
      const dataWithMyPlans = { ...data, my_plans: (data as any).my_plans || null } as UserProfileData;
      if (shouldHandleTrialExpiration(dataWithMyPlans)) {
        await handleTrialExpiration(dataWithMyPlans);
        // Refetch data after expiration handling
        const { data: updatedData, error: refetchError } = await supabase
          .from('users')
          .select('*')
          .eq('uid', userId)
          .single();

        if (!refetchError && updatedData) {
          // Use updatedData instead of reassigning data
          const profileData = {
            ...updatedData,
            my_plans: (updatedData as any).my_plans || null
          } as UserProfileData;

          setProfileData(profileData);

          // Calculate and set trial information with updated data
          const updatedTrial = getTrialInfo(updatedData);
          setTrialInfo(updatedTrial);

          // Fetch additional data with updated profile
          await Promise.all([
            fetchUserStats(userId),
            fetchPlanLimits(profileData.my_plans)
          ]);

          return; // Exit early since we've set the updated data
        }
      }

      // Convert data to proper type
      const profileData = {
        ...data,
        my_plans: (data as any).my_plans || null
      } as UserProfileData;

      setProfileData(profileData);

      // Calculate and set trial information
      const trial = getTrialInfo(data);
      setTrialInfo(trial);

      // Update limits based on user's plan if they don't have limits set
      if (profileData.my_plans && (!profileData.unlock_limit || !profileData.readcode_limit || !profileData.other_operations_limit)) {
        const planLimits = await fetchPlanLimits(profileData.my_plans);
        if (planLimits) {
          const limitsToUpdate: any = {};
          if (!data.unlock_limit) limitsToUpdate.unlock_limit = planLimits.unlock_limit;
          if (!data.readcode_limit) limitsToUpdate.readcode_limit = planLimits.readcode_limit;
          if (!data.other_operations_limit) limitsToUpdate.other_operations_limit = planLimits.other_operations_limit;

          if (Object.keys(limitsToUpdate).length > 0) {
            const { data: updatedData, error: updateError } = await supabase
              .from('users')
              .update(limitsToUpdate)
              .eq('uid', userId)
              .select()
              .single();

            if (!updateError && updatedData) {
              const updatedProfileData = {
                ...updatedData,
                my_plans: (updatedData as any).my_plans || null
              } as UserProfileData;

              setProfileData(updatedProfileData);
              // Update trial info with new data
              const updatedTrial = getTrialInfo(updatedData);
              setTrialInfo(updatedTrial);
            }
          }
        }
      }
    } catch (err) {
      setError('Failed to fetch user profile');
    }
  };

  // Create user profile in users table if it doesn't exist
  const createUserProfile = async (userId: string): Promise<boolean> => {
    if (!authUser) {
      return false;
    }

    // Show loading state during profile creation
    setLoading(true);
    setError(null);

    // Show creating profile notification
    toast.info('Setting up your profile...', {
      description: 'Creating your 14-day free trial account'
    });

    try {
      // Create user data with 14-day free trial
      const newUserData = createTrialUserData(authUser);

      const { data, error } = await supabase
        .from('users')
        .insert([newUserData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      const createdProfileData = {
        ...data,
        my_plans: (data as any).my_plans || null
      } as UserProfileData;

      setProfileData(createdProfileData);

      // Set trial information
      const trial = getTrialInfo(data);
      setTrialInfo(trial);

      toast.success('Welcome! Your 14-day free trial has started!', {
        description: `You have unlimited access until ${trial.trialEndDate?.toLocaleDateString()}`
      });

      // Fetch additional data
      await Promise.all([
        fetchUserStats(userId),
        fetchPlanLimits(createdProfileData.my_plans)
      ]);

      // Set loading to false after successful creation
      setLoading(false);
      return true; // Success

    } catch (err: any) {
      // Handle duplicate key error (user already exists)
      if (err?.code === '23505') {
        try {
          // Try to fetch the existing user profile
          const { data: existingData, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('uid', userId)
            .single();

          if (!fetchError && existingData) {
            const dataWithMyPlans = { ...existingData, my_plans: (existingData as any).my_plans || null } as UserProfileData;
            setProfileData(dataWithMyPlans);

            // Calculate trial info
            const trial = getTrialInfo(dataWithMyPlans);
            setTrialInfo(trial);

            // Fetch additional data
            await Promise.all([
              fetchUserStats(userId),
              fetchPlanLimits(dataWithMyPlans.my_plans)
            ]);

            setLoading(false);
            return true; // Success - found existing profile
          }
        } catch (fetchErr) {
          // Silent error handling
        }
      }

      // Provide more specific error messages for other errors
      let errorMessage = 'Failed to create user profile';
      if (err?.code === '23503') {
        errorMessage = 'Database constraint error';
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      toast.error(errorMessage);
      setLoading(false); // Set loading to false on error too
      return false; // Failure
    }
  };

  // Handle trial expiration
  const handleTrialExpiration = async (userData: UserProfileData) => {
    try {
      const expirationData = getTrialExpirationData();

      const { error } = await supabase
        .from('users')
        .update(expirationData)
        .eq('uid', userData.uid);

      if (error) throw error;

      toast.info('Your free trial has expired', {
        description: 'Please choose a plan to continue using our services.'
      });
    } catch (err) {
      // Silent error handling
    }
  };

  // Fetch user statistics and activity
  const fetchUserStats = async (userId: string) => {
    try {
      // Get operations count and recent activity (including credit field to check payment type)
      const { data: operations, error: opsError } = await supabase
        .from('operations')
        .select('operation_type, time, status, model, brand, credit')
        .eq('uid', userId)
        .order('time', { ascending: false });

      if (opsError) throw opsError;

      // Calculate stats
      const totalOperations = operations?.length || 0;
      const recentOps = operations?.slice(0, 5) || [];

      // Helper function to check if operation is free (counts against plan limits)
      const isFreeOperation = (op: any) => {
        // Logic:
        // - Free operations (use plan limits): credit is null, empty, "0", or "free"
        // - Paid operations (don't count against limits): credit has a positive value
        const creditValue = op.credit;

        if (!creditValue || creditValue === null || creditValue === '') {
          return true; // No credit info = free operation
        }

        const creditStr = String(creditValue).toLowerCase().trim();

        // Check for explicit free indicators
        if (creditStr === '0' || creditStr === 'free' || creditStr === 'مجاني') {
          return true; // Explicitly marked as free
        }

        // Check if it's a positive number (paid operation)
        const creditNum = parseFloat(creditStr);
        if (!isNaN(creditNum) && creditNum > 0) {
          return false; // Paid with credits
        }

        // Default to free if unclear
        return true;
      };

      // Calculate operations by type (only count FREE operations that use plan limits)
      const unlockOperationsUsed = operations?.filter(op =>
        isFreeOperation(op) && (
          op.operation_type?.toLowerCase().includes('unlock') ||
          op.operation_type?.toLowerCase().includes('direct unlock')
        )
      ).length || 0;

      const readcodeOperationsUsed = operations?.filter(op =>
        isFreeOperation(op) && (
          op.operation_type?.toLowerCase().includes('read') ||
          op.operation_type?.toLowerCase().includes('code')
        )
      ).length || 0;

      const otherOperationsUsed = operations?.filter(op =>
        isFreeOperation(op) && (
          !op.operation_type?.toLowerCase().includes('unlock') &&
          !op.operation_type?.toLowerCase().includes('direct unlock') &&
          !op.operation_type?.toLowerCase().includes('read') &&
          !op.operation_type?.toLowerCase().includes('code')
        )
      ).length || 0;

      // Calculate total free vs paid operations for additional insights
      const totalFreeOperations = operations?.filter(op => isFreeOperation(op)).length || 0;
      const totalPaidOperations = operations?.filter(op => !isFreeOperation(op)).length || 0;

      // Calculate security score based on account status and 2FA
      let securityScore = 70; // Base score
      if (profileData?.two_factor_enabled) securityScore += 20;
      if (profileData?.block === 'no') securityScore += 10;

      // Mock some additional stats that would typically come from analytics
      const profileViews = Math.floor(totalOperations * 2.5) + Math.floor(Math.random() * 100);
      const userRating = Math.min(5.0, 3.5 + (totalOperations * 0.01));

      const stats: UserStats = {
        totalSessions: totalOperations, // Using operations as sessions for now
        totalOperations,
        joinDate: authUser?.created_at ? new Date(authUser.created_at).toLocaleDateString() : '',
        lastLogin: authUser?.last_sign_in_at ? new Date(authUser.last_sign_in_at).toLocaleDateString() : '',
        accountStatus: profileData?.block === 'yes' ? 'blocked' :
                      (profileData?.expiry_time && new Date(profileData.expiry_time) < new Date()) ? 'expired' : 'active',
        securityScore,
        profileViews,
        userRating: Math.round(userRating * 10) / 10,
        unlockOperationsUsed,
        readcodeOperationsUsed,
        otherOperationsUsed,
        totalFreeOperations,
        totalPaidOperations
      };

      setUserStats(stats);
      setRecentActivity(recentOps.map(op => ({
        operation_type: op.operation_type,
        time: op.time,
        status: op.status,
        model: op.model,
        brand: op.brand
      })));

    } catch (err) {
      setError('Failed to fetch user statistics');
    }
  };

  // Fetch plan limits based on user's current plan
  const fetchPlanLimits = async (planName: string | null) => {
    if (!planName) return null;

    // Handle special cases for trial/free plans
    if (planName === 'Free' || planName === 'Free Trial' || planName === 'trial') {
      return {
        unlock_limit: '999999',
        readcode_limit: '999999',
        other_operations_limit: '999999'
      };
    }

    try {
      const { data: planData, error } = await supabase
        .from('pricing')
        .select('*')
        .ilike('name_plan', `%${planName}%`)
        .single();

      if (error || !planData) {
        return null;
      }

      // Extract limits from plan features or set defaults based on plan name
      const lowerPlanName = planName.toLowerCase();
      let limits = {
        unlock_limit: '0',
        readcode_limit: '0',
        other_operations_limit: '0'
      };

      // Set limits based on plan name (fallback if not in database)
      if (lowerPlanName.includes('basic')) {
        limits = { unlock_limit: '50', readcode_limit: '0', other_operations_limit: '250' };
      } else if (lowerPlanName.includes('plus') && !lowerPlanName.includes('max')) {
        limits = { unlock_limit: '100', readcode_limit: '0', other_operations_limit: '500' };
      } else if (lowerPlanName.includes('pro') && !lowerPlanName.includes('max')) {
        limits = { unlock_limit: '250', readcode_limit: '0', other_operations_limit: '1500' };
      } else if (lowerPlanName.includes('max') && !lowerPlanName.includes('pro')) {
        limits = { unlock_limit: '500', readcode_limit: '100', other_operations_limit: '2500' };
      } else if (lowerPlanName.includes('max pro') && !lowerPlanName.includes('plus')) {
        limits = { unlock_limit: '1000', readcode_limit: '200', other_operations_limit: 'Unlimited' };
      } else if (lowerPlanName.includes('max pro plus')) {
        limits = { unlock_limit: '1500', readcode_limit: '300', other_operations_limit: 'Unlimited' };
      }

      return limits;
    } catch (err) {
      return null;
    }
  };

  // Update user profile
  const updateUserProfile = async (updates: Partial<UserProfileData>) => {
    if (!authUser || !profileData) return { error: 'No user data available' };

    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('uid', authUser.id)
        .select()
        .single();

      if (error) throw error;

      const updatedProfileData = {
        ...data,
        my_plans: (data as any).my_plans || null
      } as UserProfileData;

      setProfileData(updatedProfileData);
      
      // Also update auth user metadata if relevant fields are updated
      const authUpdates: any = {};
      if (updates.name) authUpdates.full_name = updates.name;
      if (updates.phone) authUpdates.phone = updates.phone;
      if (updates.country) authUpdates.location = updates.country;

      if (Object.keys(authUpdates).length > 0) {
        await supabase.auth.updateUser({
          data: authUpdates
        });
      }

      return { error: null };
    } catch (err) {
      return { error: 'Failed to update profile' };
    }
  };

  // Main effect to fetch data when user changes
  useEffect(() => {
    let isCancelled = false;

    const fetchData = async () => {
      if (!authUser || isCancelled) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        await fetchUserProfile(authUser.id);
      } catch (err) {
        // Silent error handling
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };

    fetchData();

    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isCancelled = true;
    };
  }, [authUser]);

  // Fetch stats when profile data is available
  useEffect(() => {
    if (profileData && authUser) {
      fetchUserStats(authUser.id);
    }
  }, [profileData, authUser]);

  return {
    profileData,
    userStats,
    recentActivity,
    trialInfo,
    loading,
    error,
    updateUserProfile,
    fetchPlanLimits,
    refetch: () => {
      if (authUser) {
        fetchUserProfile(authUser.id);
      }
    }
  };
};
