// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://sxigocnatqgqgiedrgue.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN4aWdvY25hdHFncWdpZWRyZ3VlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNTY1ODgsImV4cCI6MjA2MDgzMjU4OH0.JaRFyEuVOC9VXoPFc7ohO77F1qM_NwY_jOgNcSacfp4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);