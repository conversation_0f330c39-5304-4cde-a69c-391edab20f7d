export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      certsave: {
        Row: {
          Email: string | null
          Hwid: string | null
          Imei: string
          ImeiSign: string | null
          Model: string | null
          Notes: string | null
          Phone_sn: string
          PubKey: string | null
          PubKeySign: string | null
          uid: string
        }
        Insert: {
          Email?: string | null
          Hwid?: string | null
          Imei: string
          ImeiSign?: string | null
          Model?: string | null
          Notes?: string | null
          Phone_sn: string
          PubKey?: string | null
          PubKeySign?: string | null
          uid: string
        }
        Update: {
          Email?: string | null
          Hwid?: string | null
          Imei?: string
          ImeiSign?: string | null
          Model?: string | null
          Notes?: string | null
          Phone_sn?: string
          PubKey?: string | null
          PubKeySign?: string | null
          uid?: string
        }
        Relationships: []
      }
      discounts: {
        Row: {
          count_refund: number | null
          email: string
          id: string
          model: string | null
          number_discounts: number | null
          uid: string
        }
        Insert: {
          count_refund?: number | null
          email: string
          id?: string
          model?: string | null
          number_discounts?: number | null
          uid: string
        }
        Update: {
          count_refund?: number | null
          email?: string
          id?: string
          model?: string | null
          number_discounts?: number | null
          uid?: string
        }
        Relationships: []
      }
      groups: {
        Row: {
          inserted_at: string | null
          key: string
          value: string | null
        }
        Insert: {
          inserted_at?: string | null
          key: string
          value?: string | null
        }
        Update: {
          inserted_at?: string | null
          key?: string
          value?: string | null
        }
        Relationships: []
      }
      offers: {
        Row: {
          created_at: string
          expiry_at: string | null
          id: string
          percentage: string | null
          status: string | null
        }
        Insert: {
          created_at?: string
          expiry_at?: string | null
          id?: string
          percentage?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string
          expiry_at?: string | null
          id?: string
          percentage?: string | null
          status?: string | null
        }
        Relationships: []
      }
      advanced_offers: {
        Row: {
          id: string
          title: string
          description: string | null
          discount_type: 'percentage' | 'fixed' | 'bogo' | 'tiered'
          discount_value: number
          min_purchase_amount: number | null
          max_discount_amount: number | null
          applicable_plans: string[]
          start_date: string | null
          end_date: string
          usage_limit: number | null
          used_count: number | null
          is_active: boolean
          urgency_level: 'low' | 'medium' | 'high'
          badge_text: string | null
          promo_code: string | null
          created_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          discount_type: 'percentage' | 'fixed' | 'bogo' | 'tiered'
          discount_value: number
          min_purchase_amount?: number | null
          max_discount_amount?: number | null
          applicable_plans: string[]
          start_date?: string | null
          end_date: string
          usage_limit?: number | null
          used_count?: number | null
          is_active?: boolean
          urgency_level: 'low' | 'medium' | 'high'
          badge_text?: string | null
          promo_code?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          discount_type?: 'percentage' | 'fixed' | 'bogo' | 'tiered'
          discount_value?: number
          min_purchase_amount?: number | null
          max_discount_amount?: number | null
          applicable_plans?: string[]
          start_date?: string | null
          end_date?: string
          usage_limit?: number | null
          used_count?: number | null
          is_active?: boolean
          urgency_level?: 'low' | 'medium' | 'high'
          badge_text?: string | null
          promo_code?: string | null
          created_at?: string
        }
        Relationships: []
      }
      operations: {
        Row: {
          android: string | null
          baseband: string | null
          brand: string | null
          carrier: string | null
          credit: string | null
          hwid: string | null
          imei: string | null
          model: string | null
          operation_id: string
          operation_type: string
          phone_sn: string | null
          security_patch: string | null
          status: string | null
          time: string | null
          uid: string | null
          username: string | null
        }
        Insert: {
          android?: string | null
          baseband?: string | null
          brand?: string | null
          carrier?: string | null
          credit?: string | null
          hwid?: string | null
          imei?: string | null
          model?: string | null
          operation_id: string
          operation_type: string
          phone_sn?: string | null
          security_patch?: string | null
          status?: string | null
          time?: string | null
          uid?: string | null
          username?: string | null
        }
        Update: {
          android?: string | null
          baseband?: string | null
          brand?: string | null
          carrier?: string | null
          credit?: string | null
          hwid?: string | null
          imei?: string | null
          model?: string | null
          operation_id?: string
          operation_type?: string
          phone_sn?: string | null
          security_patch?: string | null
          status?: string | null
          time?: string | null
          uid?: string | null
          username?: string | null
        }
        Relationships: []
      }
      payment_methods: {
        Row: {
          description: string | null
          id: string
          image_url: string | null
          method: string
        }
        Insert: {
          description?: string | null
          id?: string
          image_url?: string | null
          method: string
        }
        Update: {
          description?: string | null
          id?: string
          image_url?: string | null
          method?: string
        }
        Relationships: []
      }
      pricing: {
        Row: {
          features: string | null
          id: string
          name_plan: string
          perks: string | null
          price: string | null
        }
        Insert: {
          features?: string | null
          id?: string
          name_plan: string
          perks?: string | null
          price?: string | null
        }
        Update: {
          features?: string | null
          id?: string
          name_plan?: string
          perks?: string | null
          price?: string | null
        }
        Relationships: []
      }
      server_history: {
        Row: {
          amount: number | null
          distributor_id: string
          id: string
          operation_details: Json
          operation_type: string
          status: string
          target_user_id: string | null
          timestamp: string
        }
        Insert: {
          amount?: number | null
          distributor_id: string
          id?: string
          operation_details?: Json
          operation_type: string
          status?: string
          target_user_id?: string | null
          timestamp?: string
        }
        Update: {
          amount?: number | null
          distributor_id?: string
          id?: string
          operation_details?: Json
          operation_type?: string
          status?: string
          target_user_id?: string | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "server_history_distributor_id_fkey"
            columns: ["distributor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "server_history_target_user_id_fkey"
            columns: ["target_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      settings: {
        Row: {
          key: string
          numeric_value: number | null
          object_name: string
          title: string | null
          value: boolean | null
        }
        Insert: {
          key: string
          numeric_value?: number | null
          object_name: string
          title?: string | null
          value?: boolean | null
        }
        Update: {
          key?: string
          numeric_value?: number | null
          object_name?: string
          title?: string | null
          value?: boolean | null
        }
        Relationships: []
      }
      supported_models: {
        Row: {
          brand: string
          carrier: string | null
          id: string
          model: string
          operation: string | null
          price: string | null
          security: string | null
        }
        Insert: {
          brand: string
          carrier?: string | null
          id?: string
          model: string
          operation?: string | null
          price?: string | null
          security?: string | null
        }
        Update: {
          brand?: string
          carrier?: string | null
          id?: string
          model?: string
          operation?: string | null
          price?: string | null
          security?: string | null
        }
        Relationships: []
      }
      download_statistics: {
        Row: {
          id: string
          download_type: string
          version: string | null
          user_agent: string | null
          download_count: number
          file_name: string | null
          file_size: number | null
          download_source: string
          referrer_url: string | null
          success: boolean
          error_message: string | null
          download_duration: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          download_type?: string
          version?: string | null
          user_agent?: string | null
          download_count?: number
          file_name?: string | null
          file_size?: number | null
          download_source?: string
          referrer_url?: string | null
          success?: boolean
          error_message?: string | null
          download_duration?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          download_type?: string
          version?: string | null
          user_agent?: string | null
          download_count?: number
          file_name?: string | null
          file_size?: number | null
          download_source?: string
          referrer_url?: string | null
          success?: boolean
          error_message?: string | null
          download_duration?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      two_factor_verification: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          token: string
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at: string
          id?: string
          token: string
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          token?: string
          user_id?: string
        }
        Relationships: []
      }
      update: {
        Row: {
          changelog: string | null
          direct_download: boolean | null
          download_count: number | null
          link: string | null
          name: string | null
          release_at: string | null
          varizon: string
        }
        Insert: {
          changelog?: string | null
          direct_download?: boolean | null
          download_count?: number | null
          link?: string | null
          name?: string | null
          release_at?: string | null
          varizon: string
        }
        Update: {
          changelog?: string | null
          direct_download?: boolean | null
          download_count?: number | null
          link?: string | null
          name?: string | null
          release_at?: string | null
          varizon?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          activate: string | null
          block: string | null
          country: string | null
          credits: string | null
          distributor_id: string | null
          email: string
          expiry_time: string | null
          hwid: string | null
          id: string
          name: string | null
          otp_secret: string | null
          other_operations_limit: string | null
          password: string
          phone: string | null
          readcode_limit: number | null
          start_date: string | null
          two_factor_enabled: boolean | null
          uid: string
          unlock_limit: number | null
          user_type: string | null
        }
        Insert: {
          activate?: string | null
          block?: string | null
          country?: string | null
          credits?: string | null
          distributor_id?: string | null
          email: string
          expiry_time?: string | null
          hwid?: string | null
          id: string
          name?: string | null
          otp_secret?: string | null
          other_operations_limit?: string | null
          password: string
          phone?: string | null
          readcode_limit?: number | null
          start_date?: string | null
          two_factor_enabled?: boolean | null
          uid: string
          unlock_limit?: number | null
          user_type?: string | null
        }
        Update: {
          activate?: string | null
          block?: string | null
          country?: string | null
          credits?: string | null
          distributor_id?: string | null
          email?: string     
          expiry_time?: string | null
          hwid?: string | null
          id?: string
          name?: string | null
          otp_secret?: string | null
          other_operations_limit?: string | null
          password?: string
          phone?: string | null
          readcode_limit?: number | null
          start_date?: string | null
          two_factor_enabled?: boolean | null
          uid?: string
          unlock_limit?: number | null
          user_type?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      deduct_credits_with_discount: {
        Args: {
          pxu: string
          pxe: string
          pxm: string
          pxc: string
          pxoi: string
          pxot: string
          pxps: string
          pxbr: string
          pxim: string
          pxst: string
          pxan: string
          pxba: string
          pxca: string
          pxse: string
          pxhw: string
        }
        Returns: string
      }
      delete_auth_user: {
        Args: { user_id: string }
        Returns: undefined
      }
      increment_counter: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_distributor: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      verify_login_status: {
        Args: { loui: string; lohw: string; lova: string }
        Returns: Json
      }
      verify_otp: {
        Args: { user_id: string; otp_code: string }
        Returns: boolean
      }
      record_download_stat: {
        Args: {
          p_download_type?: string
          p_version?: string
          p_user_agent?: string
          p_file_name?: string
          p_file_size?: number
          p_download_source?: string
          p_referrer_url?: string
          p_success?: boolean
          p_error_message?: string
          p_download_duration?: number
        }
        Returns: Json
      }
      get_download_statistics: {
        Args: {
          p_start_date?: string
          p_end_date?: string
          p_download_type?: string
          p_version?: string
          p_group_by?: string
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
