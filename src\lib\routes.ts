// Route configuration and utilities for authentication

export const AUTH_ROUTES = [
  '/sign-in',
  '/sign-up',
  '/forgot-password',
  '/reset-password',
  '/auth/callback'
];

export const PROTECTED_ROUTES = [
  '/profile',
  '/dashboard',
  '/settings',
  '/account'
];

export const PUBLIC_ROUTES = [
  '/',
  '/software',
  '/hardware',
  '/supported-models',
  '/resellers',
  '/payment-methods',
  '/contact',
  '/pricing',
  '/whats-new',
  '/knowledge-base',
  '/help-center',
  '/faq',
  '/terms-of-service',
  '/privacy-policy'
];

/**
 * Check if a route requires authentication
 */
export const isProtectedRoute = (pathname: string): boolean => {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route));
};

/**
 * Check if a route is an authentication route
 */
export const isAuthRoute = (pathname: string): boolean => {
  return AUTH_ROUTES.includes(pathname);
};

/**
 * Check if a route is public (doesn't require authentication)
 */
export const isPublicRoute = (pathname: string): boolean => {
  return PUBLIC_ROUTES.includes(pathname) || pathname === '/';
};

/**
 * Get the default redirect path after authentication
 */
export const getDefaultRedirectPath = (): string => {
  return '/';
};

/**
 * Validate if a redirect path is safe
 */
export const isSafeRedirectPath = (path: string): boolean => {
  // Check for null, undefined, or empty string
  if (!path || typeof path !== 'string') {
    console.warn('Invalid redirect path type:', typeof path, path);
    return false;
  }

  // Trim whitespace and check for suspicious characters
  const trimmedPath = path.trim();

  // Check for malformed paths (like just "@" or other single characters)
  if (trimmedPath.length <= 1 && trimmedPath !== '/') {
    console.warn('Suspicious redirect path detected:', trimmedPath);
    return false;
  }

  // Prevent redirecting to external URLs or auth pages
  if (trimmedPath.startsWith('http') || trimmedPath.startsWith('//')) {
    console.warn('External URL redirect blocked:', trimmedPath);
    return false;
  }

  // Don't redirect to auth pages
  if (isAuthRoute(trimmedPath)) {
    console.warn('Auth route redirect blocked:', trimmedPath);
    return false;
  }

  // Check for URL encoding issues - decode and validate
  try {
    const decodedPath = decodeURIComponent(trimmedPath);
    if (decodedPath !== trimmedPath) {
      console.log('URL decoded path:', trimmedPath, '->', decodedPath);
      // Recursively validate the decoded path
      return isSafeRedirectPath(decodedPath);
    }
  } catch (error) {
    console.warn('URL decoding failed for path:', trimmedPath, error);
    return false;
  }

  return true;
};

/**
 * Get a safe redirect path, fallback to default if unsafe
 */
export const getSafeRedirectPath = (path: string | null): string => {
  console.log('getSafeRedirectPath called with:', path);

  if (!path || !isSafeRedirectPath(path)) {
    const defaultPath = getDefaultRedirectPath();
    console.log('Using default redirect path:', defaultPath);
    return defaultPath;
  }

  const safePath = path.trim();
  console.log('Using provided redirect path:', safePath);
  return safePath;
};

/**
 * Debug function to check localStorage redirect state
 */
export const debugRedirectState = (): void => {
  try {
    const savedRedirect = localStorage.getItem('redirectAfterAuth');
    console.log('=== REDIRECT DEBUG INFO ===');
    console.log('localStorage redirectAfterAuth:', savedRedirect);
    console.log('Type:', typeof savedRedirect);
    console.log('Length:', savedRedirect?.length);
    console.log('Is safe:', savedRedirect ? isSafeRedirectPath(savedRedirect) : 'N/A');
    console.log('Current URL:', window.location.href);
    console.log('Current pathname:', window.location.pathname);
    console.log('=========================');
  } catch (error) {
    console.error('Error debugging redirect state:', error);
  }
};
