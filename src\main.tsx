
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import LoadingAnimation from './components/LoadingAnimation'
import { StrictMode } from 'react'
import { AuthProvider } from './contexts/AuthContext'
import { cspProtection } from './utils/cspProtection'

// Initialize CSP protection
cspProtection.init();

// Register Service Worker for additional security headers
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ Service Worker registered:', registration.scope);
      })
      .catch((error) => {
        console.log('❌ Service Worker registration failed:', error);
      });
  });
}

// إضافة فئة التحميل إلى الجسم
document.body.classList.add('loading');

// إزالة أي عناصر mask-overlay موجودة مسبقًا
const existingMaskOverlay = document.getElementById('mask-overlay');
if (existingMaskOverlay && existingMaskOverlay.parentNode) {
  existingMaskOverlay.parentNode.removeChild(existingMaskOverlay);
}

// إنشاء عنصر قناع للتأثير الدائري
const maskOverlay = document.createElement('div');
maskOverlay.id = 'mask-overlay';
document.body.appendChild(maskOverlay);

// إزالة أي عناصر loading-root موجودة مسبقًا
const existingLoadingRoot = document.getElementById('loading-root');
if (existingLoadingRoot && existingLoadingRoot.parentNode) {
  existingLoadingRoot.parentNode.removeChild(existingLoadingRoot);
}

// إنشاء حاوية لرسوم التحميل المتحركة
const loadingRoot = document.createElement('div');
loadingRoot.id = 'loading-root';
loadingRoot.className = 'perspective-container';
document.body.appendChild(loadingRoot);

// عرض رسوم التحميل المتحركة
createRoot(loadingRoot).render(<LoadingAnimation />);

// عرض التطبيق الرئيسي مع StrictMode لاكتشاف المشاكل المحتملة
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = createRoot(rootElement);
  root.render(
    <StrictMode>
      <AuthProvider>
        <App />
      </AuthProvider>
    </StrictMode>
  );
}