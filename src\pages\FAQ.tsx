
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, ChevronDown, ChevronUp, HelpCircle, MessageCircle,
  Filter, BookOpen, Clock, Star, TrendingUp, Users, CheckCircle,
  AlertCircle, Info, Lightbulb, Shield, Zap, ArrowRight
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [expandedQuestions, setExpandedQuestions] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Toggle FAQ question expansion
  const toggleQuestion = (id: string) => {
    setExpandedQuestions(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const query = formData.get('search') as string;
    if (query.trim()) {
      toast.info(`Searching for: ${query}`);
      setSearchQuery(query);
    } else {
      toast.warning('Please enter a search query');
    }
  };

  // FAQ stats
  const faqStats = [
    { icon: HelpCircle, value: "150+", label: "Questions Answered", color: "text-blue-400" },
    { icon: Clock, value: "< 1 min", label: "Average Read Time", color: "text-green-400" },
    { icon: Star, value: "4.8/5", label: "Helpfulness Rating", color: "text-yellow-400" },
    { icon: TrendingUp, value: "95%", label: "Resolution Rate", color: "text-purple-400" },
  ];

  // Categories with enhanced data
  const categories = [
    { id: 'general', name: 'General Questions', icon: HelpCircle, count: 25, color: 'text-blue-400' },
    { id: 'account', name: 'Account & Licensing', icon: Shield, count: 18, color: 'text-green-400' },
    { id: 'technical', name: 'Technical Issues', icon: Zap, count: 32, color: 'text-red-400' },
    { id: 'features', name: 'Features & Usage', icon: BookOpen, count: 28, color: 'text-purple-400' },
    { id: 'compatibility', name: 'Device Compatibility', icon: CheckCircle, count: 22, color: 'text-yellow-400' }
  ];
  
  // FAQ Data
  const faqData = {
    general: [
      { 
        id: 'what-is-pegasus',
        question: 'What is Pegasus Tool?',
        answer: 'Pegasus Tool is a professional software solution designed for smartphone flashing, unlocking, and repair operations. It supports a wide range of devices including Xiaomi, Vivo, Oppo, Realme, and many others.'
      },
      { 
        id: 'cost',
        question: 'How much does Pegasus Tool cost?',
        answer: 'Pegasus Tool is available in different license types including monthly and yearly subscriptions. Please check our Pricing section for detailed information.'
      },
      { 
        id: 'customer-support',
        question: 'Do you offer customer support?',
        answer: 'Yes, we provide comprehensive customer support through various channels including email, live chat, and phone. Our support team is available to assist with any issues you might encounter.'
      }
    ],
    account: [
      { 
        id: 'create-account',
        question: 'How do I create an account?',
        answer: 'To create an account, click the "Sign Up" button on our website, fill out the registration form with your details, and follow the verification instructions sent to your email.'
      },
      { 
        id: 'forgot-password',
        question: 'I forgot my password. How can I reset it?',
        answer: 'You can reset your password by clicking on the "Forgot Password" link on the login page. Enter your registered email address and follow the instructions sent to your inbox.'
      },
      { 
        id: 'transfer-license',
        question: 'Can I transfer my license to another computer?',
        answer: 'Yes, you can deactivate your license on one computer and activate it on another. Go to Settings > License > Deactivate in the application on your current computer, then activate it on the new one.'
      }
    ],
    technical: [
      { 
        id: 'device-not-detected',
        question: 'My device is not being detected by Pegasus Tool',
        answer: 'First, ensure you have the proper USB drivers installed. Try using a different USB cable or port. Make sure USB debugging is enabled on your device. Restart both your computer and device. If the issue persists, contact our support team.'
      },
      { 
        id: 'installation-error',
        question: 'I\'m getting installation errors',
        answer: 'Make sure you\'re running the installer as administrator. Temporarily disable your antivirus software as it might be blocking the installation. Ensure your system meets the minimum requirements. Clear temporary files and try reinstalling.'
      },
      { 
        id: 'operation-failed',
        question: 'Why do I get "Operation Failed" errors?',
        answer: 'This can happen for several reasons: incompatible device firmware, connection issues, insufficient device battery, or locked bootloader. Check our troubleshooting guide for specific error codes and solutions.'
      }
    ],
    features: [
      { 
        id: 'unlock-bootloader',
        question: 'Can Pegasus Tool unlock my device\'s bootloader?',
        answer: 'Yes, Pegasus Tool supports bootloader unlocking for many supported devices. However, the process varies by manufacturer and model. Some devices may require additional authentication or waiting periods set by the manufacturer.'
      },
      { 
        id: 'data-backup',
        question: 'Does the tool include data backup features?',
        answer: 'Yes, Pegasus Tool includes comprehensive backup and restore functions for contacts, messages, apps, and other user data. We recommend always creating a backup before performing any flashing or unlocking operations.'
      },
      { 
        id: 'remove-frp',
        question: 'Can Pegasus Tool remove FRP locks?',
        answer: 'Pegasus Tool includes features to address FRP (Factory Reset Protection) on supported devices. This feature is intended for legitimate use cases where the device owner has forgotten their credentials.'
      }
    ],
    compatibility: [
      { 
        id: 'check-compatibility',
        question: 'How do I check if my device is compatible?',
        answer: 'You can check our Supported Models section on the website, which is regularly updated with new devices. You can search for your specific brand and model to verify compatibility.'
      },
      { 
        id: 'unsupported-device',
        question: 'What if my device is not listed as supported?',
        answer: 'If your device is not currently listed, it may be added in future updates. You can contact our support team to request device support or to inquire about potential workarounds for similar models.'
      },
      { 
        id: 'windows-compatibility',
        question: 'Which Windows versions are supported?',
        answer: 'Pegasus Tool is compatible with Windows 10 and Windows 11. It may work on Windows 8.1, but we officially support and recommend Windows 10/11 for the best experience and full feature set.'
      }
    ]
  };
  
  // Get current FAQs based on active category
  const currentFaqs = faqData[activeCategory as keyof typeof faqData] || [];
  
  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Animated particles */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-green-500/5"
          style={{
            width: Math.random() * 60 + 30,
            height: Math.random() * 60 + 30,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -100, 0],
            x: [0, Math.random() * 60 - 30, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 20 + 15,
            repeat: Infinity,
            delay: Math.random() * 10,
          }}
        />
      ))}

      <div className="relative z-10 pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="text-center mb-16"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-green-400 px-4 py-1 rounded-full text-sm font-medium">
                FAQ Center
              </span>
            </motion.div>

            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-white mb-4">
              Frequently Asked <span className="text-green-400">Questions</span>
            </motion.h1>

            <motion.p variants={itemVariants} className="text-lg text-gray-400 max-w-3xl mx-auto mb-10">
              Find instant answers to the most common questions about Pegasus Tool
            </motion.p>

            {/* Search section */}
            <motion.form variants={itemVariants} onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative flex items-center">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  name="search"
                  type="text"
                  placeholder="Search for questions, topics, or keywords..."
                  className="pl-12 pr-20 py-4 w-full text-base bg-[#1a1a1a] border border-gray-700 rounded-full text-white placeholder:text-gray-400 focus:border-green-400 focus:outline-none"
                />
                <Button
                  type="submit"
                  className="absolute right-2 bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-2 transition-all duration-300 hover:scale-105"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </motion.form>
          </motion.div>

          {/* FAQ Stats */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {faqStats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="text-center"
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-green-400/50 transition-all duration-300 hover:-translate-y-2">
                      <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-green-900/30 to-green-800/20 rounded-full mb-4 mx-auto">
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                      <h3 className="text-xl font-bold text-white mb-1">{stat.value}</h3>
                      <p className="text-gray-400 text-sm">{stat.label}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* FAQ Categories */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-12"
          >
            <motion.div variants={itemVariants} className="text-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                Browse by <span className="text-green-400">Category</span>
              </h2>
              <p className="text-gray-400">
                Select a category to find answers to specific questions
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <motion.div
                    key={category.id}
                    variants={itemVariants}
                    className="group cursor-pointer"
                    onClick={() => {
                      setActiveCategory(category.id);
                      setExpandedQuestions([]);
                    }}
                  >
                    <div className={`bg-[#1a1a1a] border rounded-xl p-4 backdrop-blur-lg transition-all duration-300 hover:-translate-y-1 ${
                      activeCategory === category.id
                        ? 'border-green-400/50 bg-green-400/10'
                        : 'border-gray-700/50 hover:border-green-400/50'
                    }`}>
                      <div className="text-center">
                        <div className={`w-12 h-12 flex items-center justify-center rounded-full mb-3 mx-auto transition-transform group-hover:scale-110 ${
                          activeCategory === category.id
                            ? 'bg-green-600'
                            : 'bg-gray-700'
                        }`}>
                          <Icon className={`h-6 w-6 ${
                            activeCategory === category.id ? 'text-white' : category.color
                          }`} />
                        </div>
                        <h3 className={`font-semibold mb-1 ${
                          activeCategory === category.id ? 'text-green-400' : 'text-white'
                        }`}>
                          {category.name}
                        </h3>
                        <p className="text-gray-400 text-xs">
                          {category.count} questions
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        
          {/* FAQ Questions */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="max-w-4xl mx-auto mb-16"
          >
            <motion.div variants={itemVariants} className="mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {categories.find(c => c.id === activeCategory)?.name || "Frequently Asked Questions"}
              </h2>
              <p className="text-gray-400">
                Click on any question to view the detailed answer
              </p>
            </motion.div>

            <div className="space-y-4">
              {currentFaqs.length > 0 ? (
                currentFaqs.map((faq, index) => (
                  <motion.div
                    key={faq.id}
                    variants={itemVariants}
                    className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg overflow-hidden"
                  >
                    <div
                      className="p-6 flex justify-between items-center cursor-pointer hover:bg-gray-800/50 transition-colors"
                      onClick={() => toggleQuestion(faq.id)}
                    >
                      <h3 className="font-semibold text-white pr-4 flex items-center gap-3">
                        <span className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {index + 1}
                        </span>
                        {faq.question}
                      </h3>
                      <Button variant="ghost" size="sm" className="shrink-0">
                        {expandedQuestions.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-green-400" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </Button>
                    </div>

                    <AnimatePresence>
                      {expandedQuestions.includes(faq.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="p-6 pt-0 border-t border-gray-700/50">
                            <div className="bg-gray-800/30 rounded-lg p-4">
                              <p className="text-gray-300 leading-relaxed whitespace-pre-line">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))
              ) : (
                <motion.div variants={itemVariants} className="text-center py-12">
                  <AlertCircle className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">
                    No FAQs available for this category at the moment.
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        
          {/* Still need help section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="bg-gradient-to-r from-green-600/20 via-green-500/10 to-green-700/20 rounded-2xl p-8 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
              <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

              <div className="relative z-10 text-center max-w-2xl mx-auto">
                <motion.div variants={itemVariants}>
                  <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <MessageCircle className="w-8 h-8 text-white" />
                  </div>

                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Still have <span className="text-green-400">questions?</span>
                  </h2>
                  <p className="text-gray-400 mb-8">
                    If you couldn't find the answers you're looking for, our expert support team is here to help you 24/7
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 transition-all duration-300 hover:scale-105 group"
                      onClick={() => toast.info('Redirecting to contact page...')}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Contact Support
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                    <Button
                      variant="outline"
                      className="border-green-400 text-green-400 hover:bg-green-400/10 px-8 py-3"
                      onClick={() => toast.info('Redirecting to knowledge base...')}
                    >
                      <BookOpen className="w-4 h-4 mr-2" />
                      Visit Knowledge Base
                    </Button>
                  </div>

                  <div className="mt-8 pt-6 border-t border-gray-700/50">
                    <div className="flex items-center justify-center gap-6 text-sm text-gray-400">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-green-400" />
                        <span>24/7 Support</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-green-400" />
                        <span>Expert Team</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span>Quick Response</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
