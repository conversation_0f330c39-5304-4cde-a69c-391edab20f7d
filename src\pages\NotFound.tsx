import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { Home, ArrowLeft, Search, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import FuzzyText from "@/components/ui/FuzzyText";

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#111111] text-white relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 z-0 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        {/* 404 with Fuzzy Effect */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <FuzzyText
            fontSize="clamp(4rem, 15vw, 12rem)"
            fontWeight={900}
            color="white"
            baseIntensity={0.3}
            hoverIntensity={0.8}
          >
            404
          </FuzzyText>
        </motion.div>
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-4xl text-gray-400 mb-12"
        >
          Page Not Found
        </motion.h1>

          <Link to="/">
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-purple-500/25"
            >
              <Home className="w-5 h-5 mr-2" />
              Go Home
            </Button>
          </Link>

        {/* Additional Help */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="mt-12 text-center"
        >
          <p className="text-gray-400 mb-4">
            Need help finding something specific?
          </p>
          <Link to="/help-center">
            <Button
              variant="ghost"
              className="text-purple-400 hover:text-purple-300 hover:bg-purple-400/10"
            >
              <Search className="w-4 h-4 mr-2" />
              Visit Help Center
            </Button>
          </Link>
        </motion.div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-1/4 left-1/6 w-4 h-4 bg-purple-400/30 rounded-full blur-sm"
        />
        <motion.div
          animate={{
            y: [0, 20, 0],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
          className="absolute top-1/3 right-1/6 w-6 h-6 bg-blue-400/30 rounded-full blur-sm"
        />
        <motion.div
          animate={{
            y: [0, -15, 0],
            rotate: [0, 3, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
          className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-pink-400/30 rounded-full blur-sm"
        />
      </div>
    </div>
  );
};

export default NotFound;
