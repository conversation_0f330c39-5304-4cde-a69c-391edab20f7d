
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Download, ChevronRight, Phone, Users } from "lucide-react";
import AnimatedCounter from '@/components/AnimatedCounter';
import PaymentMethods from '@/sections/PaymentMethods';
import Resellers from '@/sections/Resellers';
import Contact from '@/sections/Contact';
import PegasusHero from '@/components/ui/hero-section-nexus';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const NewHome = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalModels: 0,
    downloadCount: 0,
    distributorsCount: 0
  });

  useEffect(() => {
    // Fetch statistics
    const fetchStats = async () => {
      try {
        // Get total models count by counting actual rows from supported_models table
        const { count: totalModelsCount } = await supabase
          .from('supported_models')
          .select('*', { count: 'exact', head: true });

        // Set distributors count as fixed value
        const distributorsCount = 23;

        // Get download count from new download_statistics table
        const { count: downloadCount } = await supabase
          .from('download_statistics')
          .select('*', { count: 'exact', head: true })
          .eq('success', true);

        setStats({
          totalModels: totalModelsCount || 0,
          downloadCount: downloadCount || 0,
          distributorsCount: distributorsCount
        });
      } catch (error) {
        // Error handling without console output
      }
    };

    fetchStats();
  }, []);

  const navigateTo = (path: string) => {
    navigate(path);
  };

  return (
    <div className="bg-[#111111] text-gray-300 min-h-screen">
      {/* Hero Section */}
      <PegasusHero 
        onSoftwareClick={() => navigateTo("/software")}
        onHardwareClick={() => navigateTo("/hardware")}
      />

      {/* Statistics Section */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
                Our Statistics
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
              Discover the impact and reach of <span className="text-purple-400">Pegasus Tool</span>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
              Professional smartphone repair and unlocking system trusted by technicians worldwide
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-6 shadow-md mx-auto">
                  <Phone className="h-8 w-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-white">Supported Models</h3>
                <p className="text-4xl font-bold text-purple-400 mt-2 mb-4">
                  <AnimatedCounter value={stats.totalModels} />
                </p>
                <p className="text-gray-400">Compatible smartphone models</p>
              </div>
            </motion.div>

            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-6 shadow-md mx-auto">
                  <Download className="h-8 w-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-white">Total Downloads</h3>
                <p className="text-4xl font-bold text-purple-400 mt-2 mb-4">
                  <AnimatedCounter value={stats.downloadCount} />
                </p>
                <p className="text-gray-400">Professionals using our solution</p>
              </div>
            </motion.div>

            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-6 shadow-md mx-auto">
                  <Users className="h-8 w-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-white">Official Distributors</h3>
                <p className="text-4xl font-bold text-purple-400 mt-2 mb-4">
                  <AnimatedCounter value={stats.distributorsCount} />
                </p>
                <p className="text-gray-400">Authorized distributors worldwide</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
                Why Choose Us
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
              Why Choose <span className="text-purple-400">Pegasus Tool</span>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
              The complete solution for device unlocking and repair services
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Complete Solution</h3>
                    <p className="text-gray-400">Integrated software and hardware tools provide a comprehensive repair and unlocking experience.</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Technical Expertise</h3>
                    <p className="text-gray-400">Access to professional-grade tools with detailed documentation and technical support.</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Regular Updates</h3>
                    <p className="text-gray-400">Frequent software updates and new hardware documentation to stay current with the latest devices.</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div variants={item}>
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Global Support</h3>
                    <p className="text-gray-400">Worldwide network of distributors and technical support available in multiple languages.</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 via-purple-500 to-purple-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-10"></div>

        {/* Animated particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-white/10"
            style={{
              width: Math.random() * 50 + 20,
              height: Math.random() * 50 + 20,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0, 0.5, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
          />
        ))}

        <div className="container mx-auto px-4 relative">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Ready to get started?</h2>
            <p className="text-lg text-white/90 mb-8">
              Choose the solution that best fits your needs - complete software tools, hardware documentation, or both.
            </p>
          </div>

          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <motion.button
              className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={() => navigateTo("/software")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative">Software Solutions</span>
            </motion.button>

            <motion.button
              className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={() => navigateTo("/hardware")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative">Hardware Documentation</span>
            </motion.button>
          </div>
        </div>
      </section>

      {/* Payment Methods Section */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>
        <div className="relative z-10">
          <PaymentMethods />
        </div>
      </section>

      {/* Resellers Section */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>
        <div className="relative z-10">
          <Resellers />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>
        <div className="relative z-10">
          <Contact />
        </div>
      </section>
    </div>
  );
};

export default NewHome;
