
import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Globe, Mail, Phone, ExternalLink } from 'lucide-react';
import StatisticsStyleHeader from '@/components/layout/StatisticsStyleHeader';

const Resellers = () => {
  const worldwideResellers = [
    {
      name: "Halabtech",
      type: "Distributor",
      website: "server.halabtech.com",
      websiteUrl: "https://server.halabtech.com",
      contact: "<EMAIL>",
      phone: "+****************",
      rating: 5
    },
    {
      name: "Leope-GSM",
      type: "Reseller",
      website: "leope-gsm.com",
      websiteUrl: "https://leope-gsm.com",
      contact: "<EMAIL>",
      phone: "+44 20 1234 5678",
      rating: 4
    },
    {
      name: "Martlobs GSM",
      type: "Reseller",
      website: "martlobsgsm.com",
      websiteUrl: "https://martlobsgsm.com",
      contact: "<EMAIL>",
      phone: "+971 50 123 4567",
      rating: 4
    }
  ];

  const europeanResellers = [
    {
      name: "EuroUnlock",
      type: "Reseller",
      website: "eurounlock.com",
      websiteUrl: "https://eurounlock.com",
      contact: "<EMAIL>",
      phone: "+33 1 23 45 67 89",
      rating: 3
    },
    {
      name: "GSM-Solutions",
      type: "Reseller",
      website: "gsm-solutions.eu",
      websiteUrl: "https://gsm-solutions.eu",
      contact: "<EMAIL>",
      phone: "+49 30 123456",
      rating: 5
    }
  ];

  const asianResellers = [
    {
      name: "AsiaGSM",
      type: "Reseller",
      website: "asiagsmunlock.com",
      websiteUrl: "https://asiagsmunlock.com",
      contact: "<EMAIL>",
      phone: "+91 98765 43210",
      rating: 2
    },
    {
      name: "MobileTechAsia",
      type: "Reseller",
      website: "mobiletechasia.com",
      websiteUrl: "https://mobiletechasia.com",
      contact: "<EMAIL>",
      phone: "+65 9876 5432",
      rating: 4
    }
  ];

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center mt-4 space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  const renderResellerCard = (reseller: any) => (
    <motion.div
      key={reseller.name}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <Card className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/20 h-full overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 to-purple-500 text-white py-3 px-4 font-semibold">
          {reseller.type === "Distributor" ? "WorldWide Distributor" : "Official Reseller"}
        </div>
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-4 text-white">{reseller.name}</h3>
          <div className="space-y-3">
            <p className="flex items-center text-gray-400">
              <span className="w-6 h-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </span>
              {reseller.name} ({reseller.type})
            </p>
            <p className="flex items-center text-gray-400 group">
              <span className="w-6 h-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                <Globe className="h-4 w-4 text-purple-400" />
              </span>
              <a
                href={reseller.websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-purple-400 transition-colors duration-200 flex items-center"
              >
                {reseller.website}
                <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </a>
            </p>
            <p className="flex items-center text-gray-400 group">
              <span className="w-6 h-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                <Mail className="h-4 w-4 text-purple-400" />
              </span>
              <a
                href={`mailto:${reseller.contact}`}
                className="hover:text-purple-400 transition-colors duration-200"
              >
                {reseller.contact}
              </a>
            </p>
            <p className="flex items-center text-gray-400 group">
              <span className="w-6 h-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                <Phone className="h-4 w-4 text-purple-400" />
              </span>
              <a
                href={`tel:${reseller.phone.replace(/\s+/g, '')}`}
                className="hover:text-purple-400 transition-colors duration-200"
              >
                {reseller.phone}
              </a>
            </p>
            <div className="flex items-center text-gray-400">
              {renderStars(reseller.rating)}
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );

  return (
    <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <StatisticsStyleHeader
          badge="Authorized Partners"
          title="Trusted Resellers And Distributors Worldwide"
          highlightWord="Trusted"
          subtitle="Connect with our verified partners for purchases and professional support"
        />

        <Tabs defaultValue="worldwide" className="max-w-5xl mx-auto">
          <div className="flex justify-center mb-8">
            <TabsList className="bg-purple-100 dark:bg-gray-800">
              <TabsTrigger value="worldwide" className="data-[state=active]:bg-[#C084FC] data-[state=active]:text-white">Worldwide</TabsTrigger>
              <TabsTrigger value="europe" className="data-[state=active]:bg-[#C084FC] data-[state=active]:text-white">Europe</TabsTrigger>
              <TabsTrigger value="asia" className="data-[state=active]:bg-[#C084FC] data-[state=active]:text-white">Asia</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="worldwide">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {worldwideResellers.map(renderResellerCard)}
            </div>
          </TabsContent>

          <TabsContent value="europe">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {europeanResellers.map(renderResellerCard)}
            </div>
          </TabsContent>

          <TabsContent value="asia">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {asianResellers.map(renderResellerCard)}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

export default Resellers;
