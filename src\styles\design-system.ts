// Global Design System for Visual Consistency
export type ThemeType = 'software' | 'hardware' | 'neutral' | 'purple';

export interface DesignTheme {
  // Background gradients
  primaryGradient: string;
  secondaryGradient: string;
  cardGradient: string;
  overlayGradient: string;

  // Colors
  primary: string;
  secondary: string;
  accent: string;
  text: string;

  // Borders and shadows
  border: string;
  shadow: string;
  hoverShadow: string;

  // Interactive states
  hover: string;
  focus: string;

  // Glass morphism effects
  glass: string;
  backdrop: string;

  // Animation colors
  glow: string;
  pulse: string;
}

export const designThemes: Record<ThemeType, DesignTheme> = {
  hardware: {
    primaryGradient: 'bg-pegasus-blue-600/5 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-pegasus-blue-900/30',
    secondaryGradient: 'bg-pegasus-blue-500/3 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-pegasus-blue-900/25',
    cardGradient: 'bg-pegasus-blue-600/8 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-pegasus-blue-900/40',
    overlayGradient: 'bg-gradient-to-br from-pegasus-blue-400/8 via-pegasus-blue-300/5 to-pegasus-blue-600/12',

    primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
    secondary: 'text-pegasus-blue-500 dark:text-pegasus-blue-300',
    accent: 'text-pegasus-blue-700 dark:text-pegasus-blue-500',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-pegasus-blue-200/50 dark:border-pegasus-blue-700/40',
    shadow: 'shadow-xl shadow-pegasus-blue-100/40 dark:shadow-pegasus-blue-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-pegasus-blue-200/50 dark:hover:shadow-pegasus-blue-800/35',

    hover: 'hover:bg-pegasus-blue-600/10 dark:hover:bg-pegasus-blue-900/25',
    focus: 'focus:ring-pegasus-blue-500/40 focus:border-pegasus-blue-500/60',

    glass: 'bg-pegasus-blue-600/5 dark:bg-gray-800/30 backdrop-blur-2xl border-pegasus-blue-200/40 dark:border-pegasus-blue-700/35',
    backdrop: 'backdrop-blur-xl bg-pegasus-blue-600/8 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(59,130,246,0.25)]',
    pulse: 'animate-pulse text-pegasus-blue-400'
  },

  software: {
    primaryGradient: 'bg-orange-600/5 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-orange-900/30',
    secondaryGradient: 'bg-orange-500/3 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-orange-900/25',
    cardGradient: 'bg-orange-600/8 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-orange-900/40',
    overlayGradient: 'bg-gradient-to-br from-orange-400/8 via-orange-300/5 to-orange-600/12',

    primary: 'text-pegasus-orange',
    secondary: 'text-orange-500 dark:text-orange-300',
    accent: 'text-orange-700 dark:text-orange-500',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-orange-200/50 dark:border-orange-700/40',
    shadow: 'shadow-xl shadow-orange-100/40 dark:shadow-orange-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-orange-200/50 dark:hover:shadow-orange-800/35',

    hover: 'hover:bg-orange-600/10 dark:hover:bg-orange-900/25',
    focus: 'focus:ring-orange-500/40 focus:border-orange-500/60',

    glass: 'bg-orange-600/5 dark:bg-gray-800/30 backdrop-blur-2xl border-orange-200/40 dark:border-orange-700/35',
    backdrop: 'backdrop-blur-xl bg-orange-600/8 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(251,146,60,0.25)]',
    pulse: 'animate-pulse text-orange-400'
  },

  neutral: {
    primaryGradient: 'bg-gray-600/3 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-gray-700/30',
    secondaryGradient: 'bg-gray-500/2 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-gray-700/25',
    cardGradient: 'bg-gray-600/5 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-gray-700/40',
    overlayGradient: 'bg-gradient-to-br from-gray-400/8 via-gray-300/5 to-gray-600/12',

    primary: 'text-gray-700 dark:text-gray-300',
    secondary: 'text-gray-600 dark:text-gray-400',
    accent: 'text-gray-800 dark:text-gray-200',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-gray-200/50 dark:border-gray-600/40',
    shadow: 'shadow-xl shadow-gray-100/40 dark:shadow-gray-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-gray-200/50 dark:hover:shadow-gray-700/35',

    hover: 'hover:bg-gray-600/8 dark:hover:bg-gray-800/25',
    focus: 'focus:ring-gray-500/40 focus:border-gray-500/60',

    glass: 'bg-gray-600/3 dark:bg-gray-800/30 backdrop-blur-2xl border-gray-200/40 dark:border-gray-600/35',
    backdrop: 'backdrop-blur-xl bg-gray-600/5 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(107,114,128,0.25)]',
    pulse: 'animate-pulse text-gray-400'
  },

  purple: {
    primaryGradient: 'bg-purple-600/5 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-purple-900/30',
    secondaryGradient: 'bg-purple-500/3 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-purple-900/25',
    cardGradient: 'bg-purple-600/8 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-purple-900/40',
    overlayGradient: 'bg-gradient-to-br from-purple-400/8 via-purple-300/5 to-purple-600/12',

    primary: 'text-[#C084FC]',
    secondary: 'text-purple-500 dark:text-purple-300',
    accent: 'text-purple-700 dark:text-purple-500',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-purple-200/50 dark:border-purple-700/40',
    shadow: 'shadow-xl shadow-purple-100/40 dark:shadow-purple-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-purple-200/50 dark:hover:shadow-purple-800/35',

    hover: 'hover:bg-purple-600/10 dark:hover:bg-purple-900/25',
    focus: 'focus:ring-purple-500/40 focus:border-purple-500/60',

    glass: 'bg-purple-600/5 dark:bg-gray-800/30 backdrop-blur-2xl border-purple-200/40 dark:border-purple-700/35',
    backdrop: 'backdrop-blur-xl bg-purple-600/8 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(192,132,252,0.25)]',
    pulse: 'animate-pulse text-[#C084FC]'
  }
};

export const getTheme = (theme: ThemeType): DesignTheme => designThemes[theme];

// Animation variants for consistent motion design
export const motionVariants = {
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },

  fadeInLeft: {
    initial: { opacity: 0, x: -30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.7, ease: "easeOut" }
  },

  fadeInRight: {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.7, ease: "easeOut" }
  },

  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.5, ease: "easeOut" }
  },

  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }
};

// Consistent spacing system
export const spacing = {
  section: 'py-20',
  sectionLarge: 'py-24',
  container: 'container mx-auto px-4',
  card: 'p-8',
  cardSmall: 'p-6',
  gap: 'gap-8',
  gapLarge: 'gap-12'
};
