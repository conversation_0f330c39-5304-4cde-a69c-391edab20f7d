// Cloudflare Protection Utilities

interface CloudflareConfig {
  zoneId: string;
  apiToken: string;
  sslEnabled: boolean;
  forceHttps: boolean;
  allowedDomain: string;
}

class CloudflareProtection {
  private config: CloudflareConfig;

  constructor() {
    const isProduction = import.meta.env.PROD;
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1';

    this.config = {
      zoneId: import.meta.env.VITE_CLOUDFLARE_ZONE_ID || '',
      apiToken: import.meta.env.VITE_CLOUDFLARE_API_TOKEN || '',
      sslEnabled: import.meta.env.VITE_SSL_ENABLED === 'true' && !isLocalhost,
      forceHttps: import.meta.env.VITE_FORCE_HTTPS === 'true' && isProduction && !isLocalhost,
      allowedDomain: 'www.pegasus-tools.com'
    };
  }

  // Validate domain and force HTTPS redirect
  enforceHttps(): void {
    // Check if current domain is allowed
    if (!this.isDomainAllowed()) {
      this.blockUnauthorizedDomain();
      return;
    }

    // Don't force HTTPS on localhost or development environments
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('localhost');

    if (this.config.forceHttps &&
        window.location.protocol !== 'https:' &&
        !isLocalhost) {
      window.location.replace(`https:${window.location.href.substring(window.location.protocol.length)}`);
    }
  }

  // Check if current domain is allowed
  isDomainAllowed(): boolean {
    const currentDomain = window.location.hostname;
    const allowedDomains = [
      this.config.allowedDomain,
      'pegasus-tools.com', // Allow without www
      'localhost', // Allow for development
      '127.0.0.1'  // Allow for local development
    ];

    return allowedDomains.includes(currentDomain);
  }

  // Block unauthorized domain access
  private blockUnauthorizedDomain(): void {
    document.body.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        text-align: center;
        padding: 20px;
      ">
        <div style="
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 20px;
          padding: 40px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          max-width: 500px;
        ">
          <div style="
            width: 80px;
            height: 80px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
          ">🛡️</div>

          <h1 style="margin: 0 0 20px; font-size: 28px; font-weight: 700;">
            Access Denied
          </h1>

          <p style="margin: 0 0 20px; font-size: 16px; opacity: 0.9; line-height: 1.6;">
            This application is restricted to authorized domains only.
          </p>

          <div style="
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #10b981;
          ">
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">
              <strong>Authorized Domain:</strong><br>
              www.pegasus-tools.com
            </p>
          </div>

          <p style="margin: 0; font-size: 14px; opacity: 0.7;">
            Current domain: ${window.location.hostname}
          </p>
        </div>
      </div>
    `;

    // Prevent any further script execution
    throw new Error('Unauthorized domain access blocked');
  }

  // Check if request is coming through Cloudflare
  isCloudflareRequest(): boolean {
    // Check for Cloudflare headers (these would be set by your backend)
    const cfRay = document.querySelector('meta[name="cf-ray"]')?.getAttribute('content');
    const cfConnectingIp = document.querySelector('meta[name="cf-connecting-ip"]')?.getAttribute('content');
    
    return !!(cfRay || cfConnectingIp);
  }

  // Get visitor's real IP (from Cloudflare headers)
  getVisitorIP(): string | null {
    const cfConnectingIp = document.querySelector('meta[name="cf-connecting-ip"]')?.getAttribute('content');
    return cfConnectingIp || null;
  }

  // Get Cloudflare Ray ID for debugging
  getRayId(): string | null {
    const cfRay = document.querySelector('meta[name="cf-ray"]')?.getAttribute('content');
    return cfRay || null;
  }

  // Security headers check
  checkSecurityHeaders(): boolean {
    const requiredHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];

    // This would typically be checked on the server side
    // Here we're just providing a client-side utility
    return true;
  }

  // Get domain information
  getDomainInfo(): { domain: string; isAuthorized: boolean } {
    const domain = window.location.hostname;
    const isAuthorized = this.isDomainAllowed();

    return {
      domain,
      isAuthorized
    };
  }

  // Initialize protection
  init(): void {
    // Enforce HTTPS
    this.enforceHttps();

    // Add security event listeners
    this.addSecurityListeners();

    // Check if coming through Cloudflare
    if (!this.isCloudflareRequest()) {
      console.warn('Request not coming through Cloudflare protection');
    }
  }

  private addSecurityListeners(): void {
    // Prevent right-click context menu (optional)
    document.addEventListener('contextmenu', (e) => {
      if (import.meta.env.PROD) {
        e.preventDefault();
      }
    });

    // Prevent F12, Ctrl+Shift+I, Ctrl+U (optional - for extra protection)
    document.addEventListener('keydown', (e) => {
      if (import.meta.env.PROD) {
        if (
          e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u')
        ) {
          e.preventDefault();
        }
      }
    });

    // Detect developer tools (optional)
    let devtools = { open: false, orientation: null };
    const threshold = 160;

    setInterval(() => {
      if (
        window.outerHeight - window.innerHeight > threshold ||
        window.outerWidth - window.innerWidth > threshold
      ) {
        if (!devtools.open) {
          devtools.open = true;
          if (import.meta.env.PROD) {
            console.clear();
            console.warn('Developer tools detected!');
          }
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }
}

// Export singleton instance
export const cloudflareProtection = new CloudflareProtection();

// Utility functions for Cloudflare API calls
export const cloudflareAPI = {
  // Enable security level
  async setSecurityLevel(level: 'off' | 'essentially_off' | 'low' | 'medium' | 'high' | 'under_attack') {
    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${cloudflareProtection['config'].zoneId}/settings/security_level`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${cloudflareProtection['config'].apiToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        value: level
      })
    });

    return response.json();
  },

  // Enable/disable bot fight mode
  async setBotFightMode(enabled: boolean) {
    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${cloudflareProtection['config'].zoneId}/bot_management`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${cloudflareProtection['config'].apiToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        fight_mode: enabled
      })
    });

    return response.json();
  },

  // Get zone analytics
  async getZoneAnalytics() {
    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${cloudflareProtection['config'].zoneId}/analytics/dashboard`, {
      headers: {
        'Authorization': `Bearer ${cloudflareProtection['config'].apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }
};

export default CloudflareProtection;
