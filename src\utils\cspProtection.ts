/**
 * Advanced CSP Protection System
 * Handles CSP meta tag issues without relying on HTTP headers
 */

export class CSPProtection {
  private observers: MutationObserver[] = [];
  private intervals: NodeJS.Timeout[] = [];
  private isProduction: boolean;
  private isActive: boolean = false;

  constructor() {
    this.isProduction = window.location.hostname.includes('pegasus-tools.com');
  }

  /**
   * Initialize CSP protection
   */
  init(): void {
    if (this.isActive) return;

    console.log('🛡️ Initializing CSP Protection...');
    
    // Initial cleanup
    this.removeCSPMetaTags();
    
    // Setup monitoring systems
    this.setupDOMObserver();
    this.setupPeriodicCleanup();
    this.setupEventListeners();
    
    this.isActive = true;
    console.log('✅ CSP Protection active');
  }

  /**
   * Remove all CSP meta tags
   */
  private removeCSPMetaTags(): number {
    const selectors = [
      'meta[http-equiv*="Content-Security-Policy"]',
      'meta[http-equiv*="content-security-policy"]',
      'meta[content*="frame-ancestors"]',
      'meta[name*="csp"]'
    ];

    let removedCount = 0;
    
    selectors.forEach(selector => {
      const tags = document.querySelectorAll(selector);
      tags.forEach(tag => {
        if (this.isProduction) {
          console.warn('🚨 CSP Protection: Removing meta tag:', tag.outerHTML);
        }
        tag.remove();
        removedCount++;
      });
    });

    return removedCount;
  }

  /**
   * Setup DOM mutation observer
   */
  private setupDOMObserver(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              this.checkAndRemoveCSPTags(element);
            }
          });
        }
        
        // Monitor attribute changes
        if (mutation.type === 'attributes' && mutation.target.nodeType === Node.ELEMENT_NODE) {
          const target = mutation.target as Element;
          if (target.tagName === 'META') {
            this.checkAndRemoveCSPTags(target);
          }
        }
      });
    });

    observer.observe(document, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['http-equiv', 'content', 'name']
    });

    this.observers.push(observer);
  }

  /**
   * Check and remove CSP tags from element
   */
  private checkAndRemoveCSPTags(element: Element): void {
    // Check if element itself is a CSP meta tag
    if (element.tagName === 'META') {
      const httpEquiv = element.getAttribute('http-equiv');
      const content = element.getAttribute('content');
      const name = element.getAttribute('name');

      if (
        (httpEquiv && httpEquiv.toLowerCase().includes('content-security-policy')) ||
        (content && content.includes('frame-ancestors')) ||
        (name && name.toLowerCase().includes('csp'))
      ) {
        if (this.isProduction) {
          console.warn('🚨 CSP Protection: Blocking meta tag:', element.outerHTML);
        }
        element.remove();
        return;
      }
    }

    // Check child elements
    if (element.querySelectorAll) {
      const cspTags = element.querySelectorAll(
        'meta[http-equiv*="Content-Security-Policy"], meta[content*="frame-ancestors"], meta[name*="csp"]'
      );
      
      cspTags.forEach(tag => {
        if (this.isProduction) {
          console.warn('🚨 CSP Protection: Blocking child meta tag:', tag.outerHTML);
        }
        tag.remove();
      });
    }
  }

  /**
   * Setup periodic cleanup
   */
  private setupPeriodicCleanup(): void {
    // Aggressive cleanup for production
    const interval = this.isProduction ? 2000 : 5000;
    
    const cleanupInterval = setInterval(() => {
      const removed = this.removeCSPMetaTags();
      if (removed > 0 && this.isProduction) {
        console.log(`🧹 Periodic cleanup: Removed ${removed} CSP meta tag(s)`);
      }
    }, interval);

    this.intervals.push(cleanupInterval);
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Cleanup on page load events
    const events = ['load', 'DOMContentLoaded', 'readystatechange'];
    
    events.forEach(eventName => {
      window.addEventListener(eventName, () => {
        setTimeout(() => {
          const removed = this.removeCSPMetaTags();
          if (removed > 0 && this.isProduction) {
            console.log(`🧹 ${eventName} cleanup: Removed ${removed} CSP meta tag(s)`);
          }
        }, 100);
      });
    });

    // Special handling for script loads using modern approach
    const scriptObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.tagName === 'SCRIPT') {
                setTimeout(() => {
                  this.removeCSPMetaTags();
                }, 50);
              }
            }
          });
        }
      });
    });

    scriptObserver.observe(document, { childList: true, subtree: true });
    this.observers.push(scriptObserver);
  }

  /**
   * Manual cleanup function
   */
  cleanup(): number {
    return this.removeCSPMetaTags();
  }

  /**
   * Get protection status
   */
  getStatus(): {
    active: boolean;
    production: boolean;
    observersCount: number;
    intervalsCount: number;
  } {
    return {
      active: this.isActive,
      production: this.isProduction,
      observersCount: this.observers.length,
      intervalsCount: this.intervals.length
    };
  }

  /**
   * Destroy protection (cleanup)
   */
  destroy(): void {
    // Disconnect observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    // Clear intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];

    this.isActive = false;
    console.log('🛑 CSP Protection destroyed');
  }
}

// Create singleton instance
export const cspProtection = new CSPProtection();

// Auto-initialize if on production domain
if (window.location.hostname.includes('pegasus-tools.com')) {
  // Initialize immediately
  cspProtection.init();
  
  // Also initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => cspProtection.init());
  }
}

// Export utility functions for manual use
export const removeCSPTags = () => cspProtection.cleanup();
export const getCSPStatus = () => cspProtection.getStatus();

// Make functions available globally for debugging
declare global {
  interface Window {
    cspProtection: CSPProtection;
    removeCSPTags: () => number;
    getCSPStatus: () => any;
  }
}

window.cspProtection = cspProtection;
window.removeCSPTags = removeCSPTags;
window.getCSPStatus = getCSPStatus;
