/**
 * Trial Management Utilities
 * Handles 14-day free trial logic and subscription status management
 */

export interface TrialInfo {
  isTrialActive: boolean;
  trialStartDate: Date | null;
  trialEndDate: Date | null;
  daysRemaining: number;
  subscriptionStatus: SubscriptionStatus;
}

export type SubscriptionStatus = 'trial' | 'active' | 'expired' | 'cancelled' | 'pending';

/**
 * Trial configuration constants
 */
export const TRIAL_CONFIG = {
  DURATION_DAYS: 14,
  TRIAL_LIMITS: {
    unlock_limit: 999999, // Unlimited during trial (integer)
    readcode_limit: 999999, // Unlimited during trial (integer)
    other_operations_limit: '999999', // Unlimited during trial (text)
  },
  DEFAULT_PLAN_NAME: 'Free',
} as const;

/**
 * Calculate trial dates for a new user
 */
export const calculateTrialDates = (): { startDate: string; endDate: string } => {
  const now = new Date();
  const startDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format
  
  const endDate = new Date(now);
  endDate.setDate(endDate.getDate() + TRIAL_CONFIG.DURATION_DAYS);
  
  return {
    startDate,
    endDate: endDate.toISOString().split('T')[0]
  };
};

/**
 * Check if a trial is currently active
 */
export const isTrialActive = (trialStartDate: string | null, trialEndDate: string | null): boolean => {
  if (!trialStartDate || !trialEndDate) return false;
  
  const now = new Date();
  const endDate = new Date(trialEndDate);
  
  return now <= endDate;
};

/**
 * Calculate days remaining in trial
 */
export const calculateTrialDaysRemaining = (trialEndDate: string | null): number => {
  if (!trialEndDate) return 0;
  
  const now = new Date();
  const endDate = new Date(trialEndDate);
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
};

/**
 * Get comprehensive trial information for a user
 */
export const getTrialInfo = (userData: {
  start_date: string | null;
  expiry_time: string | null;
  user_type: string | null;
  my_plans?: string | null;
}): TrialInfo => {
  const trialStartDate = userData.start_date ? new Date(userData.start_date) : null;
  const trialEndDate = userData.expiry_time ? new Date(userData.expiry_time) : null;
  const isDateActive = isTrialActive(userData.start_date, userData.expiry_time);
  const daysRemaining = calculateTrialDaysRemaining(userData.expiry_time);

  // Determine subscription status based on user_type and my_plans
  let subscriptionStatus: SubscriptionStatus = 'expired';
  let isUserTrialActive = false;

  // Check if user is actually on a trial (Free Trial plan or trial user_type)
  const isOnFreeTrial = userData.my_plans === 'Free Trial' ||
                       userData.my_plans === 'Free' ||
                       userData.user_type === 'trial';

  if (isOnFreeTrial && isDateActive) {
    subscriptionStatus = 'trial';
    isUserTrialActive = true;
  } else if (userData.user_type === 'premium' ||
             userData.user_type === 'active' ||
             userData.user_type === 'Monthly License' ||
             userData.user_type === 'Yearly License' ||
             userData.user_type === 'Credits License' ||
             (userData.my_plans && userData.my_plans !== 'Free Trial' && userData.my_plans !== 'Free')) {
    subscriptionStatus = 'active';
    isUserTrialActive = false;
  } else if (userData.user_type === 'expired') {
    subscriptionStatus = 'expired';
    isUserTrialActive = false;
  } else {
    subscriptionStatus = 'expired';
    isUserTrialActive = false;
  }

  return {
    isTrialActive: isUserTrialActive,
    trialStartDate,
    trialEndDate,
    daysRemaining: isUserTrialActive ? daysRemaining : 0,
    subscriptionStatus
  };
};

/**
 * Get user limits based on trial status
 */
export const getUserLimits = (trialInfo: TrialInfo, planLimits?: {
  unlock_limit?: number | string;
  readcode_limit?: number | string;
  other_operations_limit?: string;
}) => {
  // If trial is active, use unlimited trial limits
  if (trialInfo.isTrialActive) {
    return {
      unlock_limit: TRIAL_CONFIG.TRIAL_LIMITS.unlock_limit,
      readcode_limit: TRIAL_CONFIG.TRIAL_LIMITS.readcode_limit,
      other_operations_limit: TRIAL_CONFIG.TRIAL_LIMITS.other_operations_limit
    };
  }

  // If trial expired and no plan limits, use zero limits
  if (!planLimits) {
    return {
      unlock_limit: 0,
      readcode_limit: 0,
      other_operations_limit: '0'
    };
  }

  // Use plan-specific limits
  return {
    unlock_limit: typeof planLimits.unlock_limit === 'string' ? parseInt(planLimits.unlock_limit) || 0 : planLimits.unlock_limit || 0,
    readcode_limit: typeof planLimits.readcode_limit === 'string' ? parseInt(planLimits.readcode_limit) || 0 : planLimits.readcode_limit || 0,
    other_operations_limit: planLimits.other_operations_limit || '0'
  };
};

/**
 * Check if "New Customer Special" offer is currently active
 */
export const checkNewCustomerSpecialOffer = async () => {
  try {
    const { supabase } = await import('@/integrations/supabase/client');

    const { data, error } = await supabase
      .from('advanced_offers')
      .select('*')
      .eq('title', 'New Customer Special')
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString())
      .single();

    if (error || !data) {
      return { isActive: false, offer: null };
    }

    // Check if start_date is valid (null means no start restriction)
    if (data.start_date && new Date(data.start_date) > new Date()) {
      return { isActive: false, offer: null };
    }

    // Check usage limit
    if (data.usage_limit && data.used_count >= data.usage_limit) {
      return { isActive: false, offer: null };
    }

    return { isActive: true, offer: data };
  } catch (error) {
    console.error('Error checking New Customer Special offer:', error);
    return { isActive: false, offer: null };
  }
};

/**
 * Update the used count for New Customer Special offer
 */
export const incrementNewCustomerOfferUsage = async (offerId: string) => {
  try {
    const { supabase } = await import('@/integrations/supabase/client');

    // First get the current used_count
    const { data: currentOffer, error: fetchError } = await supabase
      .from('advanced_offers')
      .select('used_count')
      .eq('id', offerId)
      .single();

    if (fetchError || !currentOffer) {
      console.error('Error fetching current offer:', fetchError);
      return;
    }

    // Then increment it
    const { error } = await supabase
      .from('advanced_offers')
      .update({
        used_count: (currentOffer.used_count || 0) + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', offerId);

    if (error) {
      console.error('Error incrementing offer usage:', error);
    }
  } catch (error) {
    console.error('Error incrementing offer usage:', error);
  }
};

/**
 * Create credits-based user data for new user registration (default when no offer)
 */
export const createCreditsUserData = (authUser: any) => {
  return {
    uid: authUser.id,
    id: authUser.id, // Use the same ID from auth.users
    email: authUser.email || '',
    name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || null,
    phone: authUser.user_metadata?.phone || null,
    country: authUser.user_metadata?.location || null,
    password: 'managed_by_supabase_auth',
    activate: 'Activate',
    block: 'Not Blocked',
    credits: '0.0',
    user_type: 'Credits License',
    my_plans: 'Credit',
    expiry_time: 'Unlimited',
    start_date: new Date().toISOString().split('T')[0], // Current date
    hwid: null,
    otp_secret: null,
    two_factor_enabled: false,
    unlock_limit: 0,
    readcode_limit: 0,
    other_operations_limit: '0',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
};

/**
 * Create trial data for new user registration (when offer is active)
 */
export const createTrialUserData = (authUser: any) => {
  const { startDate, endDate } = calculateTrialDates();

  return {
    uid: authUser.id,
    id: authUser.id, // Use the same ID from auth.users
    email: authUser.email || '',
    name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || null,
    phone: authUser.user_metadata?.phone || null,
    country: authUser.user_metadata?.location || null,
    password: 'managed_by_supabase_auth',
    activate: 'Activate',
    block: 'Not Blocked',
    credits: '0.0',
    user_type: 'trial', // Use existing field instead of subscription_status
    my_plans: 'Free Trial', // Changed from DEFAULT_PLAN_NAME to explicit 'Free Trial'
    expiry_time: endDate, // Use existing field instead of trial_end_date
    start_date: startDate, // Use existing field instead of trial_start_date
    hwid: null,
    otp_secret: null,
    two_factor_enabled: false,
    // Set unlimited limits during trial
    unlock_limit: TRIAL_CONFIG.TRIAL_LIMITS.unlock_limit,
    readcode_limit: TRIAL_CONFIG.TRIAL_LIMITS.readcode_limit,
    other_operations_limit: TRIAL_CONFIG.TRIAL_LIMITS.other_operations_limit
  };
};

/**
 * Check if user needs trial expiration handling
 */
export const shouldHandleTrialExpiration = (userData: {
  user_type: string | null;
  expiry_time: string | null;
  my_plans?: string | null;
}): boolean => {
  // Check if user is actually on a trial (Free Trial plan or trial user_type)
  const isOnFreeTrial = userData.my_plans === 'Free Trial' ||
                       userData.my_plans === 'Free' ||
                       userData.user_type === 'trial';

  if (!isOnFreeTrial) return false;
  if (!userData.expiry_time) return false;

  const now = new Date();
  const endDate = new Date(userData.expiry_time);

  return now > endDate;
};

/**
 * Get trial expiration data for updating user
 */
export const getTrialExpirationData = () => {
  return {
    user_type: 'Credits License',
    expiry_time: 'Unlimited',
    my_plans: 'Credit',
    unlock_limit: 0,
    readcode_limit: 0,
    other_operations_limit: '0'
  };
};
